
دليل تفعيل الترخيص من السحابة
===============================

لتفعيل تراخيص البرنامج من السحابة، اتبع الخطوات التالية:

1. إنشاء مفاتيح الترخيص:
   - استخدم ملف activate_device.py لإنشاء مفتاح ترخيص لكل جهاز
   - أدخل معرف الجهاز الذي أرسله العميل
   - انقر على "إنشاء مفتاح الترخيص"
   - سيتم إنشاء المفتاح ونسخه تلقائياً

2. تفعيل المفتاح في السحابة:
   - افتح ملف cloud_activation.html في متصفح الويب
   - أدخل معرف الجهاز ومفتاح الترخيص الذي تم إنشاؤه
   - انقر على "تفعيل الترخيص"
   - سيتم إرسال البيانات إلى السحابة وتخزينها

3. نشر CloudWorker:
   - أنشئ حساباً على Cloudflare Workers أو أي خدمة مشابهة
   - انشر الكود الموجود في ملف cloud_activation_worker.js
   - عدل الرابط في ملف cloud_activation.html ليشير إلى عنوان الـ Worker الخاص بك

4. التحقق من الترخيص:
   - عندما يحاول العميل تفعيل البرنامج، سيتم التحقق من وجود الترخيص في السحابة
   - إذا كان الترخيص موجوداً وصالحاً، سيتم تفعيل البرنامج

ملاحظات:
- يمكنك تعديل مدة صلاحية الترخيص في ملف cloud_activation_worker.js
- تأكد من تأمين نقطة نهاية الـ API لمنع الوصول غير المصرح به
- يمكنك إضافة المزيد من الميزات مثل حظر الأجهزة أو تجديد التراخيص
