<?php
// ملف التحقق من الترخيص - server_check.php
// ضع هذا الملف على الخادم الخاص بك

header('Content-Type: text/plain');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'license_db';
$username = 'your_db_user';
$password = 'your_db_password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    echo "ERROR";
    exit();
}

// التحقق من البيانات المرسلة
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo "INVALID";
    exit();
}

$machine_id = isset($_POST['machine_id']) ? trim($_POST['machine_id']) : '';
$license_key = isset($_POST['license_key']) ? trim($_POST['license_key']) : '';
$app_name = isset($_POST['app_name']) ? trim($_POST['app_name']) : '';

if (empty($machine_id) || empty($license_key)) {
    echo "INVALID";
    exit();
}

// البحث عن الترخيص في قاعدة البيانات
$stmt = $pdo->prepare("
    SELECT * FROM licenses 
    WHERE license_key = ? AND app_name = ? 
    LIMIT 1
");
$stmt->execute([$license_key, $app_name]);
$license = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$license) {
    echo "INVALID";
    exit();
}

// التحقق من حالة الترخيص
if ($license['status'] !== 'active') {
    echo "BLOCKED";
    exit();
}

// التحقق من تاريخ انتهاء الصلاحية
$expires = strtotime($license['expires_at']);
$now = time();

if ($expires < $now) {
    echo "EXPIRED";
    exit();
}

// التحقق من معرف الجهاز
if (!empty($license['machine_id']) && $license['machine_id'] !== $machine_id) {
    echo "INVALID";
    exit();
}

// تحديث معرف الجهاز إذا كان فارغاً (أول استخدام)
if (empty($license['machine_id'])) {
    $update_stmt = $pdo->prepare("
        UPDATE licenses 
        SET machine_id = ?, last_used = NOW() 
        WHERE license_key = ?
    ");
    $update_stmt->execute([$machine_id, $license_key]);
} else {
    // تحديث آخر استخدام
    $update_stmt = $pdo->prepare("
        UPDATE licenses 
        SET last_used = NOW() 
        WHERE license_key = ?
    ");
    $update_stmt->execute([$license_key]);
}

// الترخيص صالح
echo "VALID";

?>