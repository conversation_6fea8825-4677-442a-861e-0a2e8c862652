import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, simpledialog, filedialog
from TikTokLive import TikTokLiveClient
from TikTokLive.events import CommentEvent, ConnectEvent, GiftEvent, DisconnectEvent
import json
import os
import queue
import keyboard
import time
import threading
from playsound import playsound
from datetime import datetime
import traceback
import re
import pyttsx3
import asyncio
import edge_tts
import tempfile
import requests
import shutil
import hashlib
import uuid
import sys

# نظام الترخيص البسيط
class SimpleLicense:
    def __init__(self):
        self.server_url = "https://streamtoklite.abdelrahmanmohamed6vd.workers.dev/check"  # رابط Cloudflare Worker
        self.machine_id = self.get_machine_id()
        self.license_key = None
        
    def get_machine_id(self):
        """الحصول على معرف فريد للجهاز"""
        mac = str(uuid.getnode())
        computer_name = os.environ.get('COMPUTERNAME', 'unknown')
        username = os.environ.get('USERNAME', 'unknown')
        
        # دمج المعرفات وإنشاء hash
        # إضافة معلومات إضافية لزيادة التفرد
        try:
            import platform
            system_info = platform.system() + platform.release()
        except:
            system_info = "unknown"
        
        try:
            import socket
            hostname = socket.gethostname()
        except:
            hostname = "unknown"
            
        unique_string = f"{mac}-{computer_name}-{username}-{system_info}-{hostname}"
        # استخدام خوارزمية أقوى وأكثر تفرداً
        return hashlib.sha256(unique_string.encode()).hexdigest()[:16].upper()

    def generate_license_key(self, device_id=None):
        """إنشاء مفتاح ترخيص بناءً على معرف الجهاز"""
        if device_id is None:
            device_id = self.machine_id
            
        # دمج معرف الجهاز مع نص ثابت ثم تشفيره
        base = "EDION-2025-" + device_id
        hashed = hashlib.md5(base.encode()).hexdigest().upper()
        # تنسيق المفتاح كـ XXXX-XXXX-XXXX-XXXX
        key = f"{hashed[:4]}-{hashed[4:8]}-{hashed[8:12]}-{hashed[12:16]}"
        return key
    
    def set_license_key(self, key):
        """تعيين مفتاح الترخيص"""
        self.license_key = key
    
    def check_license_online(self):
        """التحقق من الترخيص عبر الإنترنت"""
        if not self.license_key:
            return False, "لم يتم إدخال مفتاح الترخيص"
            
        try:
            # استخدام طريقة GET مع المعلمات في الرابط
            params = {
                'key': self.license_key,
                'device': self.machine_id
            }
            
            response = requests.get(self.server_url, params=params, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                status = result.get('status', '')
                
                if status == 'valid':
                    return True, "الترخيص صالح"
                elif status == 'expired':
                    return False, "انتهت صلاحية الاشتراك"
                elif status == 'invalid':
                    return False, "مفتاح ترخيص غير صالح"
                elif status == 'device_mismatch':
                    # محاولة تفعيل المفتاح للجهاز الحالي
                    try:
                        activate_url = self.server_url.replace('/check', '/activate')
                        data = {
                            'licenseKey': self.license_key,
                            'deviceId': self.machine_id
                        }
                        
                        activate_response = requests.post(activate_url, json=data, timeout=10)
                        
                        if activate_response.status_code == 200:
                            activate_result = activate_response.json()
                            activate_status = activate_result.get('status', '')
                            
                            if activate_status == 'activated':
                                # إعادة محاولة التحقق بعد التفعيل
                                check_response = requests.get(self.server_url, params=params, timeout=10)
                                
                                if check_response.status_code == 200:
                                    check_result = check_response.json()
                                    check_status = check_result.get('status', '')
                                    
                                    if check_status == 'valid':
                                        return True, "تم تفعيل المفتاح والتحقق بنجاح"
                        
                        return False, "فشل تفعيل المفتاح للجهاز الحالي"
                    except:
                        return False, "الجهاز غير مسجل لاستخدام هذا الترخيص"
                else:
                    return False, "استجابة غير معروفة من الخادم"
            else:
                return False, f"خطأ في الخادم: {response.status_code}"
                
        except requests.exceptions.Timeout:
            return False, "انتهت مهلة الاتصال بالخادم"
        except requests.exceptions.ConnectionError:
            return False, "لا يمكن الاتصال بالخادم"
        except Exception as e:
            return False, f"خطأ: {str(e)}"
    
    def show_license_dialog(self, parent=None):
        """إظهار نافذة إدخال مفتاح الترخيص"""
        dialog = tk.Toplevel(parent) if parent else tk.Tk()
        dialog.title("تفعيل الترخيص")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        
        # جعل النافذة في المقدمة
        dialog.transient(parent)
        dialog.grab_set()
        
        # تصميم النافذة
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill='both', expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="تفعيل البرنامج", font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # معرف الجهاز
        ttk.Label(main_frame, text="معرف الجهاز:").pack(anchor='w')
        machine_entry = ttk.Entry(main_frame, width=40)
        machine_entry.insert(0, self.machine_id)
        machine_entry.config(state='readonly')
        machine_entry.pack(fill='x', pady=(5, 15))
        
        # مفتاح الترخيص
        ttk.Label(main_frame, text="مفتاح الترخيص:").pack(anchor='w')
        license_entry = ttk.Entry(main_frame, width=40)
        license_entry.pack(fill='x', pady=(5, 15))
        
        # رسالة الحالة
        status_label = ttk.Label(main_frame, text="", foreground='red')
        status_label.pack(pady=(0, 15))
        
        result = {'success': False}
        
        def verify_license():
            key = license_entry.get().strip()
            if not key:
                status_label.config(text="يرجى إدخال مفتاح الترخيص", foreground='red')
                return
            
            status_label.config(text="جاري التحقق...", foreground='blue')
            dialog.update()
            
            self.set_license_key(key)
            success, message = self.check_license_online()
            
            if success:
                status_label.config(text=message, foreground='green')
                result['success'] = True
                dialog.after(1500, dialog.destroy)
            else:
                status_label.config(text=message, foreground='red')
        
        # الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill='x', pady=(10, 0))
        
        ttk.Button(buttons_frame, text="تحقق", command=verify_license).pack(side='left', padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", command=dialog.destroy).pack(side='left')
        
        # التركيز على حقل الترخيص
        license_entry.focus()
        
        # ربط Enter بالتحقق
        license_entry.bind('<Return>', lambda e: verify_license())
        
        dialog.wait_window()
        return result['success']

# إنشاء مثيل من نظام الترخيص
license_manager = SimpleLicense()
import logging
import ctypes
from ctypes import wintypes
from PIL import Image, ImageTk
from io import BytesIO

# إخفاء رسائل TikTokLive التحذيرية
logging.getLogger('TikTokLive').setLevel(logging.CRITICAL)
logging.getLogger('TikTokLive.client').setLevel(logging.CRITICAL)
logging.getLogger('TikTokLive.client.client').setLevel(logging.CRITICAL)

# إخفاء جميع رسائل ERROR من TikTokLive
class TikTokLiveFilter(logging.Filter):
    def filter(self, record):
        return not (record.name.startswith('TikTokLive') and 'Failed to parse room ID' in record.getMessage())

# تطبيق الفلتر على جميع المسجلات
for handler in logging.root.handlers:
    handler.addFilter(TikTokLiveFilter())
    
# إضافة الفلتر للمسجل الجذر أيضاً
logging.root.addFilter(TikTokLiveFilter())

class ModernButton(tk.Frame):
    def __init__(self, master=None, text="", command=None, style_name=None, width=None, height=30, **kwargs):
        # الحصول على لون الخلفية من الماستر
        try:
            if hasattr(master, 'cget'):
                master_bg = master.cget('bg')
            else:
                master_bg = '#1e1e1e'
        except:
            master_bg = '#1e1e1e'
            
        super().__init__(master, bg=master_bg)
        
        # إعداد الألوان والأنماط الافتراضية
        self.colors = self.get_style_colors(style_name)
        self.text = text
        self.command = command
        self.width = width or max(len(text) * 10 + 40, 100)
        self.height = height
        self.radius = 10  # نصف قطر الحواف المدورة
        
        # إنشاء Canvas للرسم
        self.canvas = tk.Canvas(
            self, 
            width=self.width, 
            height=self.height,
            highlightthickness=0,
            bd=0,
            bg=master_bg
        )
        self.canvas.pack()
        
        # رسم الزر
        self.draw_button()
        
        # ربط الأحداث
        self.canvas.bind("<Button-1>", self.on_click)
        self.canvas.bind("<Enter>", self.on_enter)
        self.canvas.bind("<Leave>", self.on_leave)
        
        # تعيين مؤشر اليد
        self.canvas.config(cursor='hand2')
    
    def get_style_colors(self, style_name):
        """الحصول على ألوان النمط المحدد"""
        styles = {
            'Connect.TButton': {
                'bg': '#56d364',
                'fg': 'white',
                'hover_bg': '#66e374'
            },
            'Keys.TButton': {
                'bg': '#8a63d2',
                'fg': 'white', 
                'hover_bg': '#9a73e2'
            },
            'Modern.TButton': {
                'bg': '#238be6',
                'fg': 'white',
                'hover_bg': '#339bf6'
            },
            'Success.TButton': {
                'bg': '#56d364',
                'fg': 'white',
                'hover_bg': '#66e374'
            },
            'Danger.TButton': {
                'bg': '#ff7b72',
                'fg': 'white',
                'hover_bg': '#ff8b82'
            },
            'Warning.TButton': {
                'bg': '#e3b341',
                'fg': 'white',
                'hover_bg': '#f3c351'
            },
            'Delete.TButton': {
                'bg': '#f85149',
                'fg': 'white',
                'hover_bg': '#ff6159'
            }
        }
        
        return styles.get(style_name, styles['Modern.TButton'])
    
    def create_rounded_rectangle(self, x1, y1, x2, y2, radius, **kwargs):
        """رسم مستطيل بحواف مدورة"""
        points = []
        
        # الزاوية اليسرى العلوية
        points.extend([x1 + radius, y1, x2 - radius, y1])  # الخط العلوي
        points.extend([x2, y1, x2, y1 + radius])  # الزاوية اليمنى العلوية
        points.extend([x2, y1 + radius, x2, y2 - radius])  # الخط الأيمن
        points.extend([x2, y2, x2 - radius, y2])  # الزاوية اليمنى السفلى
        points.extend([x2 - radius, y2, x1 + radius, y2])  # الخط السفلي
        points.extend([x1, y2, x1, y2 - radius])  # الزاوية اليسرى السفلى
        points.extend([x1, y2 - radius, x1, y1 + radius])  # الخط الأيسر
        points.extend([x1, y1, x1 + radius, y1])  # الزاوية اليسرى العلوية
        
        return self.canvas.create_polygon(points, smooth=True, **kwargs)
    
    def draw_button(self, is_hover=False):
        """رسم الزر بحواف مدورة"""
        # مسح المحتوى السابق
        self.canvas.delete("all")
        
        # اختيار اللون المناسب
        bg_color = self.colors.get('hover_bg', self.colors['bg']) if is_hover else self.colors['bg']
        
        # رسم الخلفية المدورة
        self.create_rounded_rectangle(
            2, 2, self.width-2, self.height-2, 
            self.radius, 
            fill=bg_color, 
            outline=""
        )
        
        # إضافة النص
        self.canvas.create_text(
            self.width//2, self.height//2,
            text=self.text,
            fill=self.colors['fg'],
            font=('Segoe UI', 10, 'bold'),
            anchor='center'
        )
    
    def on_click(self, event):
        """معالجة النقر على الزر"""
        if self.command:
            self.command()
    
    def on_enter(self, event):
        """تأثير دخول الماوس - تغيير خفيف في اللون"""
        self.draw_button(is_hover=True)
    
    def on_leave(self, event):
        """تأثير خروج الماوس - العودة للون الأصلي"""
        self.draw_button(is_hover=False)
    
    def pack(self, **kwargs):
        """تخصيص دالة pack"""
        super().pack(**kwargs)
    
    def grid(self, **kwargs):
        """تخصيص دالة grid"""
        super().grid(**kwargs)
    
    def place(self, **kwargs):
        """تخصيص دالة place"""
        super().place(**kwargs)
    
    def config(self, **kwargs):
        """تخصيص دالة config لدعم الخصائص الأساسية"""
        if 'text' in kwargs:
            self.text = kwargs['text']
            self.draw_button()
        if 'command' in kwargs:
            self.command = kwargs['command']
        if 'state' in kwargs:
            state = kwargs['state']
            if state == 'disabled':
                self.canvas.config(cursor='arrow')
                # يمكن إضافة تأثير بصري للحالة المعطلة
            else:
                self.canvas.config(cursor='hand2')
    
    def configure(self, **kwargs):
        """اختصار لـ config"""
        self.config(**kwargs)

class StreamTokGUI:
    def __init__(self, root):
        """تهيئة الواجهة الرسومية"""
        self.root = root
        self.root.title("StreamTok V 2")
        self.root.geometry("1000x830")
        
        # تطبيق النمط الداكن
        self.root.configure(bg='#1e1e1e')
        style = ttk.Style()
        style.theme_use('clam')  # استخدام سمة clam بدلاً من default لدعم أفضل للألوان المخصصة
        
        # تكوين النمط الداكن
        style.configure('.', background='#1e1e1e', foreground='#ffffff')  # تطبيق النمط الداكن على جميع العناصر
        style.configure('TFrame', background='#1e1e1e')
        style.configure('TLabel', background='#1e1e1e', foreground='#ffffff')
        style.configure('TButton', background='#ff0050', foreground='#ffffff', borderwidth=0, relief='flat', padding=[10, 5])
        style.map('TButton', background=[('active', '#e60045'), ('pressed', '#cc003d')])
        style.configure('Treeview', background='#2d2d2d', foreground='#ffffff', fieldbackground='#2d2d2d', borderwidth=0)
        style.configure('Treeview.Heading', background='#333333', foreground='#ffffff')
        style.configure('TEntry', background='#2d2d2d', foreground='#ffffff', fieldbackground='#2d2d2d', borderwidth=1)
        style.map('TEntry', fieldbackground=[('focus', '#333333')])
        # تحسين نمط Combobox الافتراضي
        style.configure('TCombobox', 
            background='#2d2d2d', 
            foreground='#ffffff', 
            fieldbackground='#2d2d2d', 
            borderwidth=2, 
            relief='flat',
            arrowcolor='#238be6',
            focuscolor='none',
            selectbackground='#238be6',
            selectforeground='white'
        )
        style.map('TCombobox', 
            fieldbackground=[('readonly', '#2d2d2d'), ('focus', '#333333'), ('active', '#333333')],
            bordercolor=[('focus', '#238be6'), ('active', '#238be6')],
            arrowcolor=[('focus', '#56d364'), ('active', '#56d364')]
        )
        
        # تحسين أزرار التبويب
        style.configure('TNotebook', background='#1e1e1e', borderwidth=0)
        style.configure('TNotebook.Tab', background='#2d2d2d', foreground='#ffffff', padding=[15, 8])
        style.map('TNotebook.Tab', background=[('selected', '#ff0050'), ('active', '#333333')])

        # تخزين الألوان الأساسية للنمط الداكن للاستخدام في الدوال الأخرى
        self.dark_bg_color = '#1e1e1e'  # لون الخلفية الداكنة
        self.dark_input_bg_color = '#2d2d2d'  # لون خلفية حقول الإدخال
        self.dark_fg_color = '#ffffff'  # لون النص
        
        # تهيئة قوائم الانتظار
        self.comments_queue = queue.Queue()
        self.likes_queue = queue.Queue()
        self.gifts_queue = queue.Queue()
        self.tts_queue = queue.Queue()
        
        # تهيئة المتغيرات
        self.client = None
        self.is_connected = False
        self.key_bindings = {}
        self.profiles = {}
        self.current_profile = None
        self.gifts_data = {}
        self.gift_images = {}  # تخزين صور الهدايا
        
        # تهيئة متغيرات الاتصال
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.connection_error_sound = os.path.join("sounds", "connection_error.mp3")
        
        # تهيئة متغيرات TTS
        self.tts_enabled = False
        self.tts_language = 'ar'
        self.is_speaking = False
        self.voices = {
            'ar': {
                'ar-EG-ShakirNeural': 'شاكر (مصري)',
                'ar-EG-SalmaNeural': 'سلمى (مصرية)',
                'ar-SA-ZariyahNeural': 'زارية (سعودية)',
                'ar-SA-HamedNeural': 'حامد (سعودي)',
                'ar-AE-FatimaNeural': 'فاطمة (إماراتية)',
                'ar-AE-HamdanNeural': 'حمدان (إماراتي)',
                'ar-KW-NouraNeural': 'نورة (كويتية)',
                'ar-KW-FahedNeural': 'فهد (كويتي)',
                'ar-QA-AmalNeural': 'أمل (قطرية)',
                'ar-QA-MoazNeural': 'معاذ (قطري)'
            },
            'en': {
                'en-US-ChristopherNeural': 'Christopher (American)',
                'en-US-JennyNeural': 'Jenny (American)',
                'en-US-GuyNeural': 'Guy (American)',
                'en-GB-SoniaNeural': 'Sonia (British)',
                'en-GB-RyanNeural': 'Ryan (British)',
                'en-AU-NatashaNeural': 'Natasha (Australian)',
                'en-AU-WilliamNeural': 'William (Australian)',
                'en-CA-ClaraNeural': 'Clara (Canadian)',
                'en-CA-LiamNeural': 'Liam (Canadian)',
                'en-IE-ConnorNeural': 'Connor (Irish)'
            }
        }
        
        # تخزين القيم المعروضة ومعرفاتها
        self.voice_name_to_id = {
            name: id for lang_voices in self.voices.values()
            for id, name in lang_voices.items()
        }
        
        # تهيئة الصوت الافتراضي
        self.selected_voice = tk.StringVar()
        default_voice = 'ar-EG-ShakirNeural' if self.tts_language == 'ar' else 'en-US-ChristopherNeural'
        self.selected_voice.set(default_voice)
        
        # إنشاء المجلدات المطلوبة
        self.sounds_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sounds')
        if not os.path.exists(self.sounds_dir):
            os.makedirs(self.sounds_dir)
            
        self.temp_dir = tempfile.mkdtemp()
        
        # تهيئة مسارات الأصوات
        self.connect_sound = os.path.join(self.sounds_dir, 'connect.wav')
        self.disconnect_sound = os.path.join(self.sounds_dir, 'disconnect.wav')
        
        # تحميل البيانات
        self.load_gifts()
        self.download_gift_images()  # تحميل صور الهدايا
        self.load_profiles()
        
        # إنشاء الواجهة
        self.setup_styles()
        self.create_gui()
        
        # تحميل اسم المستخدم المحفوظ بعد إنشاء الواجهة
        if hasattr(self, 'last_saved_username') and self.last_saved_username:
            self.username_var.set(self.last_saved_username)
        
        # تحديث العرض
        self.update_keys_display()
        self.update_profile_name()
        
        # تطبيق النمط الأسود على النافذة الرئيسية
        self.root.after(100, self.apply_dark_window_theme)
        
        # بدء معالجة الرسائل
        self.root.after(100, self.process_messages)
    
    def setup_styles(self):
        """تهيئة الأنماط الحديثة والجميلة"""
        style = ttk.Style()
        
        # الألوان الأساسية للتصميم الحديث - نمط داكن حديث
        bg_primary = '#0d1117'      # خلفية أساسية داكنة جداً
        bg_secondary = '#161b22'    # خلفية ثانوية
        bg_tertiary = '#21262d'     # خلفية عناصر
        bg_surface = '#30363d'      # خلفية السطح
        
        fg_primary = '#f0f6fc'      # نص أساسي أبيض ثلجي
        fg_secondary = '#8b949e'    # نص ثانوي رمادي
        fg_muted = '#6e7681'        # نص مخفت
        
        # ألوان التحكم - نمط حديث ومبهج
        accent_blue = '#238be6'     # أزرق حديث
        accent_green = '#46a758'    # أخضر حديث
        accent_purple = '#8a63d2'   # بنفسجي حديث  
        accent_orange = '#da7633'   # برتقالي حديث
        accent_red = '#f85149'      # أحمر حديث
        accent_pink = '#db61a2'     # وردي حديث
        
        # ألوان الحالة
        success_color = '#56d364'   # نجاح
        warning_color = '#e3b341'   # تحذير
        error_color = '#ff7b72'     # خطأ
        info_color = '#79c0ff'      # معلومات
        
        # تطبيق النمط الداكن الحديث على جميع العناصر
        style.configure('.', 
            background=bg_primary, 
            foreground=fg_primary,
            font=('Segoe UI', 10)
        )
        
        # نمط الإطارات الرئيسية
        style.configure('Main.TFrame', 
            background=bg_primary,
            relief='flat',
            borderwidth=0
        )
        
        style.configure('Header.TFrame', 
            background=bg_secondary,
            relief='flat',
            borderwidth=0
        )
        
        style.configure('Card.TFrame',
            background=bg_tertiary,
            relief='flat',
            borderwidth=0
        )
        
        # نمط التسميات الحديثة
        style.configure('Modern.TLabel',
            background=bg_primary,
            foreground=fg_primary,
            font=('Segoe UI', 10)
        )
        
        style.configure('Title.TLabel',
            background=bg_primary,
            foreground=fg_primary,
            font=('Segoe UI', 14, 'bold')
        )
        
        style.configure('Subtitle.TLabel',
            background=bg_primary,
            foreground=fg_secondary,
            font=('Segoe UI', 10)
        )
        
        style.configure('Status.TLabel',
            background=bg_primary,
            foreground=info_color,
            font=('Segoe UI', 11, 'bold')
        )
        
        # نمط حقول الإدخال الحديثة
        style.configure('Modern.TEntry',
            fieldbackground=bg_surface,
            foreground=fg_primary,
            font=('Segoe UI', 10),
            borderwidth=1,
            relief='solid',
            insertcolor=fg_primary
        )
        style.map('Modern.TEntry',
            fieldbackground=[('focus', bg_tertiary)],
            bordercolor=[('focus', accent_blue)],
            lightcolor=[('focus', accent_blue)],
            darkcolor=[('focus', accent_blue)]
        )
        
        style.configure('Username.TEntry',
            fieldbackground=bg_surface,
            foreground=fg_primary,
            font=('Segoe UI', 11),
            borderwidth=2,
            relief='solid',
            insertcolor=accent_blue
        )
        style.map('Username.TEntry',
            fieldbackground=[('focus', bg_tertiary)],
            bordercolor=[('focus', accent_blue)],
            lightcolor=[('focus', accent_blue)],
            darkcolor=[('focus', accent_blue)]
        )
        
        # نمط الأزرار الحديثة مع تدرجات وظلال
        style.configure('Modern.TButton',
            background=accent_blue,
            foreground='white',
            padding=(20, 10),
            font=('Segoe UI', 10, 'bold'),
            borderwidth=0,
            relief='flat',
            focuscolor='none'
        )
        style.map('Modern.TButton',
            background=[('active', '#2ea043'), ('pressed', '#238be6')],
            relief=[('pressed', 'flat')]
        )
        
        # نمط زر الاتصال - تصميم مميز
        style.configure('Connect.TButton',
            background=success_color,
            foreground='white',
            padding=(25, 12),
            font=('Segoe UI', 11, 'bold'),
            borderwidth=0,
            relief='flat',
            focuscolor='none'
        )
        style.map('Connect.TButton',
            background=[('active', '#46a758'), ('pressed', '#2ea043')]
        )
        
        # نمط أزرار المفاتيح - تصميم جذاب
        style.configure('Keys.TButton',
            background=accent_purple,
            foreground='white',
            padding=(20, 10),
            font=('Segoe UI', 10, 'bold'),
            borderwidth=0,
            relief='flat',
            focuscolor='none'
        )
        style.map('Keys.TButton',
            background=[('active', '#9a6ddb'), ('pressed', '#8a63d2')]
        )
        
        # نمط زر الحذف - لون تحذيري
        style.configure('Delete.TButton',
            background=error_color,
            foreground='white',
            padding=(20, 10),
            font=('Segoe UI', 10, 'bold'),
            borderwidth=0,
            relief='flat',
            focuscolor='none'
        )
        style.map('Delete.TButton',
            background=[('active', '#ff8a80'), ('pressed', '#f85149')]
        )
        
        # نمط أزرار إضافية ملونة
        style.configure('Success.TButton',
            background=success_color,
            foreground='white',
            padding=(15, 8),
            font=('Segoe UI', 10, 'bold'),
            borderwidth=0,
            relief='flat'
        )
        
        style.configure('Warning.TButton',
            background=warning_color,
            foreground='white',
            padding=(15, 8),
            font=('Segoe UI', 10, 'bold'),
            borderwidth=0,
            relief='flat'
        )
        
        style.configure('Danger.TButton',
            background=error_color,
            foreground='white',
            padding=(15, 8),
            font=('Segoe UI', 10, 'bold'),
            borderwidth=0,
            relief='flat'
        )
        
        # نمط الإطارات المحيطة الحديثة
        style.configure('Modern.TLabelframe',
            background=bg_primary,
            foreground=fg_primary,
            font=('Segoe UI', 11, 'bold'),
            borderwidth=1,
            relief='solid',
            bordercolor=bg_surface
        )
        
        style.configure('Modern.TLabelframe.Label',
            background=bg_primary,
            foreground=accent_blue,
            font=('Segoe UI', 11, 'bold')
        )
        
        # نمط القوائم المنسدلة الحديثة
        style.configure('Custom.TCombobox',
            background=bg_surface,
            foreground=fg_primary,
            fieldbackground=bg_surface,
            arrowcolor=accent_purple,
            font=('Segoe UI', 10),
            borderwidth=2,
            relief='flat',
            focuscolor='none',
            selectbackground=accent_purple,
            selectforeground='white',
            insertcolor=accent_purple
        )
        style.map('Custom.TCombobox',
            fieldbackground=[
                ('readonly', bg_surface), 
                ('focus', bg_tertiary),
                ('active', bg_tertiary)
            ],
            bordercolor=[
                ('focus', accent_purple),
                ('active', accent_purple)
            ],
            arrowcolor=[
                ('focus', accent_blue),
                ('active', accent_blue)
            ]
        )
        
        style.configure('Modern.TCombobox',
            background=bg_surface,
            foreground=fg_primary,
            fieldbackground=bg_surface,
            arrowcolor=accent_blue,
            font=('Segoe UI', 10),
            borderwidth=2,
            relief='flat',
            focuscolor='none',
            selectbackground=accent_blue,
            selectforeground='white',
            insertcolor=accent_blue
        )
        style.map('Modern.TCombobox',
            fieldbackground=[
                ('readonly', bg_surface), 
                ('focus', bg_tertiary),
                ('active', bg_tertiary)
            ],
            bordercolor=[
                ('focus', accent_blue),
                ('active', accent_blue)
            ],
            arrowcolor=[
                ('focus', accent_green),
                ('active', accent_green),
                ('pressed', accent_purple)
            ],
            lightcolor=[('focus', accent_blue)],
            darkcolor=[('focus', accent_blue)]
        )
        
        # نمط أزرار الراديو الحديثة
        style.configure('Modern.TRadiobutton',
            background=bg_primary,
            foreground=fg_primary,
            font=('Segoe UI', 10),
            focuscolor='none'
        )
        style.map('Modern.TRadiobutton',
            background=[('selected', bg_primary)],
            foreground=[('selected', accent_blue)]
        )
        
        # نمط الجدول الحديث
        style.configure('Modern.Treeview',
            background=bg_tertiary,
            foreground=fg_primary,
            fieldbackground=bg_tertiary,
            font=('Segoe UI', 9),
            borderwidth=0,
            relief='flat',
            rowheight=30
        )
        
        style.configure('Modern.Treeview.Heading',
            background=bg_surface,
            foreground=fg_primary,
            font=('Segoe UI', 10, 'bold'),
            relief='flat',
            borderwidth=1
        )
        
        style.map('Modern.Treeview',
            background=[('selected', accent_blue)],
            foreground=[('selected', 'white')]
        )
        
        style.map('Modern.Treeview.Heading',
            background=[('active', bg_tertiary)]
        )
        
        # نمط الإطارات الإضافية
        style.configure('Custom.TLabelframe',
            background=bg_primary,
            foreground=fg_primary,
            font=('Segoe UI', 11, 'bold'),
            borderwidth=1,
            relief='solid',
            bordercolor=bg_surface
        )
        
        style.configure('Custom.TLabelframe.Label',
            background=bg_primary,
            foreground=accent_purple,
            font=('Segoe UI', 11, 'bold')
        )
        
        style.configure('Custom.TFrame',
            background=bg_primary
        )
        
        style.configure('Custom.TEntry',
            fieldbackground=bg_surface,
            foreground=fg_primary,
            font=('Segoe UI', 10),
            borderwidth=1,
            relief='solid'
        )
        
        style.configure('Custom.TLabel',
            background=bg_primary,
            foreground=fg_primary,
            font=('Segoe UI', 10)
        )
        
        style.configure('Custom.TRadiobutton',
            background=bg_primary,
            foreground=fg_primary,
            font=('Segoe UI', 10),
            focuscolor='none'
        )
        
        # نمط الحوارات الحديثة
        style.configure('Dialog.TFrame', background=bg_primary)
        style.configure('Dialog.TLabel', background=bg_primary, foreground=fg_primary, font=('Segoe UI', 10))
        style.configure('Dialog.TLabelframe', background=bg_primary, foreground=fg_primary, relief='flat', borderwidth=1, bordercolor=bg_surface)
        style.configure('Dialog.TLabelframe.Label', background=bg_primary, foreground=accent_blue, font=('Segoe UI', 10, 'bold'))
        style.configure('Dialog.TRadiobutton', background=bg_primary, foreground=fg_primary, relief='flat', borderwidth=0, font=('Segoe UI', 10))
        
        # نمط شريط التمرير
        style.configure('Modern.Vertical.TScrollbar',
            background=bg_tertiary,
            troughcolor=bg_surface,
            borderwidth=0,
            arrowcolor=fg_secondary,
            darkcolor=bg_tertiary,
            lightcolor=bg_tertiary
        )
        
        # تعيين خلفية النافذة الرئيسية
        self.root.configure(bg=bg_primary)
        
        # حفظ الألوان للاستخدام في أماكن أخرى
        self.colors = {
            'bg_primary': bg_primary,
            'bg_secondary': bg_secondary,
            'bg_tertiary': bg_tertiary,
            'bg_surface': bg_surface,
            'fg_primary': fg_primary,
            'fg_secondary': fg_secondary,
            'accent_blue': accent_blue,
            'accent_green': accent_green,
            'accent_purple': accent_purple,
            'success_color': success_color,
            'warning_color': warning_color,
            'error_color': error_color,
            'info_color': info_color
        }
    
    def load_gifts(self):
        """تحميل قائمة الهدايا من الرابط"""
        try:
            gifts_url = "https://webcast.tiktok.com/webcast/gift/list/?aid=1988"
            response = requests.get(gifts_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                # استخراج الهدايا من الاستجابة
                if 'data' in data and isinstance(data['data'], dict):
                    gift_list = data['data'].get('gifts', [])
                    self.gifts_data = {str(gift['id']): gift for gift in gift_list if 'id' in gift}
                    self.log_comment(f"✅ تم تحميل {len(self.gifts_data)} هدية بنجاح")
                else:
                    self.log_comment("❌ تنسيق البيانات غير صالح")
            else:
                self.log_comment(f"❌ خطأ في تحميل قائمة الهدايا: {response.status_code}")
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحميل قائمة الهدايا: {str(e)}")
            self.gifts_data = {}  # تهيئة قاموس فارغ في حالة الفشل
    
    def download_gift_images(self):
        """تحميل صور الهدايا"""
        if not self.gifts_data:
            return
            
        # إنشاء مجلد الصور إذا لم يكن موجوداً
        images_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'gift_images')
        if not os.path.exists(images_dir):
            os.makedirs(images_dir)
            
        self.log_comment(f"🖼️ تجهيز صور {len(self.gifts_data)} هدية...")
        
        # تحميل الصور الموجودة أولاً (بدون انتظار)
        self._load_existing_images(images_dir)
        
        # تحميل الصور المفقودة في الخلفية
        threading.Thread(target=self._download_missing_images, args=(images_dir,), daemon=True).start()
    
    def _load_existing_images(self, images_dir):
        """تحميل الصور الموجودة مسبقاً"""
        loaded = 0
        for gift_id in self.gifts_data.keys():
            image_filename = f"gift_{gift_id}.png"
            image_path = os.path.join(images_dir, image_filename)
            
            if os.path.exists(image_path):
                self._load_gift_image(gift_id, image_path)
                loaded += 1
        
        if loaded > 0:
            self.log_comment(f"✅ تم تحميل {loaded} صورة هدية محفوظة")
    
    def _download_missing_images(self, images_dir):
        """تحميل الصور المفقودة في الخلفية"""
        downloaded = 0
        missing = 0
        
        for gift_id, gift_data in self.gifts_data.items():
            image_filename = f"gift_{gift_id}.png"
            image_path = os.path.join(images_dir, image_filename)
            
            # تخطي الصور الموجودة
            if os.path.exists(image_path):
                continue
                
            missing += 1
            
            try:
                # الحصول على رابط الصورة
                image_url = None
                if 'image' in gift_data and 'url_list' in gift_data['image'] and gift_data['image']['url_list']:
                    image_url = gift_data['image']['url_list'][0]
                elif 'icon' in gift_data and 'url_list' in gift_data['icon'] and gift_data['icon']['url_list']:
                    image_url = gift_data['icon']['url_list'][0]
                    
                if not image_url:
                    continue
                    
                # تحميل الصورة
                response = requests.get(image_url, timeout=5)  # تقليل وقت الانتظار
                if response.status_code == 200:
                    # حفظ الصورة
                    with open(image_path, 'wb') as f:
                        f.write(response.content)
                    
                    # تحميل الصورة للذاكرة
                    self._load_gift_image(gift_id, image_path)
                    downloaded += 1
                    
                    # تحديث الواجهة إذا لزم الأمر
                    if hasattr(self, 'gift_combobox'):
                        self.root.after(0, lambda: self._refresh_gift_combo())
                        
            except Exception as e:
                continue
        
        if missing > 0:
            self.root.after(0, lambda: self.log_comment(f"🔄 تم تحميل {downloaded} من {missing} صورة مفقودة في الخلفية"))
    
    def _load_gift_image(self, gift_id, image_path):
        """تحميل صورة هدية واحدة للذاكرة"""
        try:
            # فتح الصورة وتغيير حجمها
            with Image.open(image_path) as img:
                # تغيير الحجم إلى 64x64 بكسل للاستخدام في القوائم (تكبير الصورة)
                img = img.resize((64, 64), Image.Resampling.LANCZOS)
                # تحويل إلى تنسيق Tkinter
                photo = ImageTk.PhotoImage(img)
                self.gift_images[gift_id] = photo
        except Exception as e:
            print(f"خطأ في تحميل صورة الهدية {gift_id}: {str(e)}")
    
    def load_profiles(self):
        """تحميل البروفايلات من الملف"""
        try:
            if os.path.exists('profiles.json'):
                with open('profiles.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # تحميل البروفايلات
                if isinstance(data, dict):
                    if 'profiles' in data:
                        self.profiles = data['profiles']
                        if 'current_profile' in data and data['current_profile'] in self.profiles:
                            self.current_profile = data['current_profile']
                            self.key_bindings = self.profiles[self.current_profile].get('bindings', {}).copy()
                    else:
                        # التوافق مع النسخ القديمة
                        self.profiles = data
                
                self.log_comment("✅ تم تحميل البروفايلات بنجاح")
            else:
                self.profiles = {}
                self.current_profile = None
                self.key_bindings = {}
                self.log_comment("ℹ️ لم يتم العثور على ملف البروفايلات")
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحميل البروفايلات: {str(e)}")
            self.profiles = {}
            self.current_profile = None
            self.key_bindings = {}
    
            self.log_comment(f"❌ خطأ في حفظ البروفايلات: {str(e)}")

    def update_profiles_list(self):
        """تحديث قائمة البروفايلات"""
        try:
            # مسح القائمة
            self.profiles_list.delete(0, tk.END)
            
            # إضافة البروفايلات
            for name in sorted(self.profiles.keys()):
                self.profiles_list.insert(tk.END, name)
                
            # تحديد البروفايل الحالي إذا وجد
            if self.current_profile:
                try:
                    index = sorted(self.profiles.keys()).index(self.current_profile)
                    self.profiles_list.selection_set(index)
                except ValueError:
                    pass
                
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحديث قائمة البروفايلات: {str(e)}")

    def on_profile_selected(self, event=None):
        """معالجة حدث اختيار بروفايل"""
        try:
            selected = self.profiles_list.get()
            if not selected:
                return
                
            # تحميل البروفايل المحدد
            profile_data = self.profiles[selected]
            self.key_bindings = profile_data.get('bindings', {}).copy()
            self.current_profile = selected
            
            # تحديث العرض
            self.update_keys_display()
            self.log_comment(f"✅ تم تحميل البروفايل: {selected}")
            
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحميل البروفايل: {str(e)}")

    def delete_profile(self):
        """حذف البروفايل المحدد"""
        try:
            selected = self.profiles_list.get()
            if not selected:
                self.log_comment("⚠️ الرجاء اختيار بروفايل للحذف")
                return
            
            if selected == self.current_profile:
                self.log_comment("❌ لا يمكن حذف البروفايل النشط!")
                return
                
            if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف البروفايل '{selected}'؟"):
                del self.profiles[selected]
                self.save_profiles()
                self.update_profiles_list()
                self.log_comment(f"✅ تم حذف البروفايل: {selected}")
                
        except Exception as e:
            self.log_comment(f"❌ خطأ في حذف البروفايل: {str(e)}")

    def manage_profiles(self):
        """إدارة البروفايلات"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إدارة البروفايلات")
        dialog.geometry("600x500")
        dialog.configure(bg='#1e1e1e')
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(dialog, style='Modern.TFrame', padding="20")
        main_frame.pack(fill='both', expand=True)

        # إطار معلومات البروفايل الحالي
        current_profile_frame = ttk.LabelFrame(main_frame, text="البروفايل الحالي", style='Modern.TLabelframe', padding="10")
        current_profile_frame.pack(fill='x', pady=(0, 10))
        
        # عرض اسم البروفايل الحالي
        current_profile_label = ttk.Label(current_profile_frame, 
            text=f"البروفايل النشط: {self.current_profile if self.current_profile else 'لا يوجد'}", 
            style='Modern.TLabel')
        current_profile_label.pack(fill='x', padx=5, pady=5)
        
        # عدد المفاتيح في البروفايل الحالي
        keys_count = 0
        if self.current_profile and self.current_profile in self.profiles:
            keys_count = len(self.profiles[self.current_profile].get('bindings', {}))
        
        self.keys_label = ttk.Label(current_profile_frame, 
            text=f"عدد المفاتيح المحفوظة: {keys_count}", 
            style='Modern.TLabel')
        self.keys_label.pack(fill='x', padx=5)

        # إطار قائمة البروفايلات
        profiles_frame = ttk.LabelFrame(main_frame, text="البروفايلات المتوفرة", style='Modern.TLabelframe', padding="10")
        profiles_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # إنشاء قائمة البروفايلات مع شريط التمرير
        profiles_list_frame = ttk.Frame(profiles_frame)
        profiles_list_frame.pack(fill='both', expand=True)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(profiles_list_frame)
        scrollbar.pack(side='right', fill='y')
        
        # قائمة البروفايلات
        profiles_listbox = tk.Listbox(profiles_list_frame, 
            bg='#2d2d2d', fg='white', 
            selectmode='single',
            yscrollcommand=scrollbar.set)
        profiles_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=profiles_listbox.yview)
        
        # تحديث قائمة البروفايلات
        for profile in sorted(self.profiles.keys()):
            profiles_listbox.insert('end', profile)
            if profile == self.current_profile:
                profiles_listbox.selection_set('end')

        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame, style='Modern.TFrame')
        buttons_frame.pack(fill='x', pady=10)

        def create_new_profile():
            """إنشاء بروفايل جديد"""
            name = simpledialog.askstring("إنشاء بروفايل", "أدخل اسم البروفايل الجديد:")
            if name:
                name = name.strip()
                if not name:
                    self.log_comment("❌ الرجاء إدخال اسم صحيح للبروفايل")
                    return
                if name in self.profiles:
                    self.log_comment("❌ هذا البروفايل موجود مسبقاً")
                    return
                
                # إنشاء بروفايل جديد
                self.profiles[name] = {'bindings': {}}
                self.current_profile = name
                self.key_bindings = {}
                self.save_profiles()
                
                # تحديث القائمة
                profiles_listbox.insert('end', name)
                profiles_listbox.selection_clear(0, 'end')
                profiles_listbox.selection_set('end')
                
                # تحديث العرض
                current_profile_label.config(text=f"البروفايل النشط: {name}")
                self.keys_label.config(text="عدد المفاتيح المحفوظة: 0")
                
                self.log_comment(f"✅ تم إنشاء البروفايل: {name}")
                
        def load_selected_profile():
            """تحميل البروفايل المحدد"""
            selection = profiles_listbox.curselection()
            if not selection:
                self.log_comment("⚠️ الرجاء تحديد بروفايل")
                return
            
            profile_name = profiles_listbox.get(selection[0])
            if profile_name in self.profiles:
                # حفظ البروفايل الحالي قبل التغيير
                if self.current_profile:
                    self.profiles[self.current_profile] = {
                        'bindings': self.key_bindings.copy()
                    }
                
                # تحميل البروفايل الجديد
                self.key_bindings = self.profiles[profile_name].get('bindings', {}).copy()
                self.current_profile = profile_name
                self.save_profiles()
                self.update_keys_display()
                
                # تحديث العرض
                current_profile_label.config(text=f"البروفايل النشط: {profile_name}")
                keys_count = len(self.key_bindings)
                self.keys_label.config(text=f"عدد المفاتيح المحفوظة: {keys_count}")
                
                self.log_comment(f"✅ تم تحميل البروفايل: {profile_name}")
                
        def delete_selected_profile():
            """حذف البروفايل المحدد"""
            selection = profiles_listbox.curselection()
            if not selection:
                self.log_comment("⚠️ الرجاء تحديد بروفايل للحذف")
                return
            
            profile_name = profiles_listbox.get(selection[0])
            if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف البروفايل '{profile_name}'؟"):
                if profile_name == self.current_profile:
                    self.current_profile = None
                    self.key_bindings = {}
                    self.update_keys_display()
                    current_profile_label.config(text="البروفايل النشط: لا يوجد")
                    self.keys_label.config(text="عدد المفاتيح المحفوظة: 0")
                
                del self.profiles[profile_name]
                self.save_profiles()
                profiles_listbox.delete(selection[0])
                self.log_comment(f"✅ تم حذف البروفايل: {profile_name}")

        def import_profiles():
            """استيراد البروفايلات"""
            file_path = filedialog.askopenfilename(
                filetypes=[("ملفات البروفايلات", "*.json"), ("جميع الملفات", "*.*")],
                title="استيراد البروفايلات"
            )
            if file_path:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        imported_profiles = json.load(f)
                    
                    # التحقق من صحة البيانات المستوردة
                    if not isinstance(imported_profiles, dict):
                        raise ValueError("تنسيق الملف غير صحيح")
                    
                    # دمج البروفايلات المستوردة مع الحالية
                    self.profiles.update(imported_profiles)
                    self.save_profiles()
                    
                    # تحديث القائمة
                    profiles_listbox.delete(0, 'end')
                    for profile in sorted(self.profiles.keys()):
                        profiles_listbox.insert('end', profile)
                    
                    self.log_comment("✅ تم استيراد البروفايلات بنجاح")
                except Exception as e:
                    self.log_comment(f"❌ خطأ في استيراد البروفايلات: {str(e)}")

        def export_profiles():
            """تصدير البروفايلات"""
            if not self.profiles:
                self.log_comment("⚠️ لا توجد بروفايلات للتصدير")
                return
            
            file_path = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("ملفات البروفايلات", "*.json"), ("جميع الملفات", "*.*")],
                title="تصدير البروفايلات"
            )
            if file_path:
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(self.profiles, f, ensure_ascii=False, indent=4)
                    self.log_comment("✅ تم تصدير البروفايلات بنجاح")
                except Exception as e:
                    self.log_comment(f"❌ خطأ في تصدير البروفايلات: {str(e)}")

        # أزرار التحكم
        ttk.Button(buttons_frame, text="➕ إنشاء", command=create_new_profile,
                  style='Success.TButton').pack(side='left', padx=5)
        
        ttk.Button(buttons_frame, text="📂 تحميل", command=load_selected_profile,
                  style='Modern.TButton').pack(side='left', padx=5)
        
        ttk.Button(buttons_frame, text="❌ حذف", command=delete_selected_profile,
                  style='Danger.TButton').pack(side='left', padx=5)
        
        ttk.Button(buttons_frame, text="📥 استيراد", command=import_profiles,
                  style='Modern.TButton').pack(side='left', padx=5)
        
        ttk.Button(buttons_frame, text="📤 تصدير", command=export_profiles,
                  style='Modern.TButton').pack(side='left', padx=5)
        
        ttk.Button(buttons_frame, text="✖️ إغلاق", command=dialog.destroy,
                  style='Modern.TButton').pack(side='right', padx=5)

        # جعل النافذة مركزية
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.focus()

    def save_profiles(self):
        """حفظ البروفايلات في الملف"""
        try:
            # تحديث البروفايل الحالي قبل الحفظ
            if self.current_profile:
                self.profiles[self.current_profile] = {
                    'bindings': self.key_bindings.copy()
                }
            
            # حفظ جميع البروفايلات مع اسم المستخدم
            data = {
                'profiles': self.profiles,
                'current_profile': self.current_profile,
                'last_username': self.username_var.get() if hasattr(self, 'username_var') else ''  # حفظ اسم المستخدم الأخير
            }
            
            with open('profiles.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
            
            # عدم عرض رسالة الحفظ في كل مرة (لأن هذه الدالة تستدعى تلقائياً)
            # self.log_comment("✅ تم حفظ البروفايلات بنجاح")
        except Exception as e:
            self.log_comment(f"❌ خطأ في حفظ البروفايلات: {str(e)}")

    def load_profiles(self):
        """تحميل البروفايلات من الملف"""
        try:
            if os.path.exists('profiles.json'):
                with open('profiles.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # تحميل البروفايلات
                if isinstance(data, dict):
                    if 'profiles' in data:
                        self.profiles = data['profiles']
                        if 'current_profile' in data and data['current_profile'] in self.profiles:
                            self.current_profile = data['current_profile']
                            self.key_bindings = self.profiles[self.current_profile].get('bindings', {}).copy()
                        # تحميل اسم المستخدم المحفوظ - سيتم استدعاؤه بعد إنشاء الواجهة
                        if 'last_username' in data:
                            self.last_saved_username = data['last_username']
                    else:
                        # التوافق مع النسخ القديمة
                        self.profiles = data
                
                self.log_comment("✅ تم تحميل البروفايلات بنجاح")
            else:
                self.profiles = {}
                self.current_profile = None
                self.key_bindings = {}
                self.log_comment("ℹ️ لم يتم العثور على ملف البروفايلات")
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحميل البروفايلات: {str(e)}")
            self.profiles = {}
            self.current_profile = None
            self.key_bindings = {}
    
    def create_gui(self):
        """إنشاء واجهة المستخدم"""
        # تهيئة النافذة الرئيسية
        main_frame = ttk.Frame(self.root, style='Main.TFrame')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # إطار التحكم العلوي
        control_frame = ttk.Frame(main_frame, style='Header.TFrame')
        control_frame.pack(fill='x', pady=(0, 10))
        
        # إطار اسم المستخدم الحديث والجميل
        username_container = ttk.Frame(control_frame, style='Card.TFrame')
        username_container.pack(side='left', padx=15, pady=10, fill='y')
        
        # تسمية حقل المستخدم
        username_label = ttk.Label(username_container, 
                                   text="🎯 اسم مستخدم TikTok",
                                   style='Subtitle.TLabel')
        username_label.pack(anchor='w', padx=10, pady=(10, 5))
        
        # حاوية حقل الإدخال مع أيقونة
        username_input_frame = ttk.Frame(username_container, style='Card.TFrame')
        username_input_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        # أيقونة المستخدم
        user_icon = ttk.Label(username_input_frame, 
                              text="👤", 
                              style='Modern.TLabel',
                              font=('Segoe UI', 12))
        user_icon.pack(side='left', padx=(5, 0))
        
        # حقل اسم المستخدم
        self.username_var = tk.StringVar()
        username_entry = ttk.Entry(username_input_frame,
                                 textvariable=self.username_var,
                                 style='Username.TEntry',
                                 width=22,
                                 font=('Segoe UI', 11))
        username_entry.pack(side='left', padx=(8, 5), fill='x', expand=True)
        
        # ربط حدث تغيير النص للحفظ التلقائي لاسم المستخدم
        self.username_var.trace_add("write", lambda *args: self.save_profiles())
        
        # زر الاتصال
        self.connect_btn = ModernButton(control_frame, text="اتصال", command=self.toggle_connection, style_name='Connect.TButton')
        self.connect_btn.pack(side='left', padx=5)
        
        # زر إضافة محاكاة مفتاح
        self.add_key_btn = ModernButton(control_frame, text="✨ إضافة محاكاة مفتاح", command=self.add_key_binding, style_name='Keys.TButton')
        self.add_key_btn.pack(side='left', padx=5)
        
        # زر إدارة البروفايلات
        manage_profiles_button = ModernButton(control_frame, text="👥 إدارة البروفايلات", 
                                          command=self.manage_profiles,
                                          style_name='Modern.TButton')
        manage_profiles_button.pack(side='left', padx=5)
        
        # حالة الاتصال
        self.status_var = tk.StringVar(value="غير متصل")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, style='Status.TLabel')
        status_label.pack(pady=5)
        
        # منطقة محاكاة المفاتيح
        keys_frame = ttk.LabelFrame(main_frame, text="محاكاة المفاتيح", style='Modern.TLabelframe')
        keys_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # إطار الأزرار لمحاكاة المفاتيح
        keys_buttons_frame = ttk.Frame(keys_frame)
        keys_buttons_frame.pack(fill='x', pady=5, padx=5)
        

        
        # زر حفظ الإعدادات - استخدام النمط الحديث
        save_btn = ModernButton(keys_buttons_frame, text="💾 حفظ الإعدادات", command=self.save_key_bindings, style_name='Success.TButton')
        save_btn.pack(side='right', padx=8)
        
        # زر اختبار التعيين المحدد - استخدام النمط الحديث
        test_btn = ModernButton(keys_buttons_frame, text="🔍 اختبار المحدد", command=self.test_selected_binding, style_name='Modern.TButton')
        test_btn.pack(side='right', padx=8)
        
        # منطقة محاكاة المفاتيح - قائمة قابلة للتعديل
        keys_list_frame = ttk.Frame(keys_frame, style='Modern.TFrame')
        keys_list_frame.pack(pady=5, padx=5, fill='both', expand=True)
        
        # إنشاء شريط تمرير
        scrollbar = ttk.Scrollbar(keys_list_frame)
        scrollbar.pack(side='right', fill='y')
        
        # إنشاء قائمة العرض الحديثة
        self.keys_tree = ttk.Treeview(
            keys_list_frame, 
            yscrollcommand=scrollbar.set,
            columns=('details', 'custom_name', 'keys', 'sound'), 
            show='tree headings',
            selectmode='browse',
            height=8,
            style='Modern.Treeview'
        )
        self.keys_tree.pack(side='left', fill='both', expand=True)
        
        # إعداد الشريط المتحرك الحديث
        scrollbar.config(command=self.keys_tree.yview, style='Modern.Vertical.TScrollbar')
        
        # إعداد العناوين
        self.keys_tree.heading('#0', text='الحدث')
        self.keys_tree.heading('details', text='التفاصيل')
        self.keys_tree.heading('custom_name', text='الاسم المخصص')
        self.keys_tree.heading('keys', text='المفاتيح')
        self.keys_tree.heading('sound', text='الصوت')
        
        # إعداد الأعمدة
        self.keys_tree.column('#0', width=120, anchor='center')
        self.keys_tree.column('details', width=250, anchor='center')
        self.keys_tree.column('custom_name', width=150, anchor='center')
        self.keys_tree.column('keys', width=180, anchor='center')
        self.keys_tree.column('sound', width=120, anchor='center')
        
        # ربط الأحداث
        self.keys_tree.bind('<Double-1>', self.edit_key_binding)
        self.keys_tree.bind('<Delete>', self.delete_key_binding)
        
        
        # إطار المحتوى
        content_frame = ttk.Frame(main_frame, style='Modern.TFrame')
        content_frame.pack(fill='both', expand=True)
        
        # تقسيم الشاشة أفقياً للتعليقات والهدايا مع مسافات متوازنة
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # منطقة التعليقات مع التصميم الحديث والألوان المميزة
        comments_frame = ttk.LabelFrame(
            content_frame, 
            text="💬 التعليقات المباشرة", 
            style='Modern.TLabelframe'
        )
        comments_frame.grid(row=0, column=0, padx=(0, 8), pady=5, sticky='nsew')
        
        # إطار داخلي للتعليقات مع حواف مميزة
        comments_inner_frame = tk.Frame(
            comments_frame,
            bg=self.colors['bg_secondary'],
            relief='flat',
            bd=1
        )
        comments_inner_frame.pack(fill='both', expand=True, padx=12, pady=12)
        
        # منطقة عرض التعليقات مع تصميم متقدم
        self.comments_area = scrolledtext.ScrolledText(
            comments_inner_frame,
            height=10,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['fg_primary'],
            font=('Segoe UI', 10),
            insertbackground=self.colors['accent_blue'],
            selectbackground=self.colors['accent_blue'],
            selectforeground='white',
            wrap='word',
            borderwidth=0,
            relief='flat',
            padx=10,
            pady=8,
            highlightthickness=0,
            selectborderwidth=0
        )
        self.comments_area.pack(fill='both', expand=True)
        
        # تخصيص شريط التمرير للتعليقات
        comments_scrollbar = self.comments_area.vbar
        comments_scrollbar.configure(
            bg=self.colors['bg_surface'],
            troughcolor=self.colors['bg_secondary'],
            activebackground=self.colors['accent_blue'],
            borderwidth=0,
            highlightthickness=0
        )
        
        # منطقة الهدايا مع التصميم الحديث والألوان المميزة
        gifts_frame = ttk.LabelFrame(
            content_frame, 
            text="🎁 الهدايا الواردة", 
            style='Modern.TLabelframe'
        )
        gifts_frame.grid(row=0, column=1, padx=(8, 0), pady=5, sticky='nsew')
        
        # إطار داخلي للهدايا مع حواف مميزة
        gifts_inner_frame = tk.Frame(
            gifts_frame,
            bg=self.colors['bg_secondary'],
            relief='flat',
            bd=1
        )
        gifts_inner_frame.pack(fill='both', expand=True, padx=12, pady=12)
        
        # منطقة عرض الهدايا مع تصميم متقدم
        self.gifts_area = scrolledtext.ScrolledText(
            gifts_inner_frame,
            height=10,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['fg_primary'],
            font=('Segoe UI', 10),
            insertbackground=self.colors['accent_green'],
            selectbackground=self.colors['accent_green'],
            selectforeground='white',
            wrap='word',
            borderwidth=0,
            relief='flat',
            padx=10,
            pady=8,
            highlightthickness=0,
            selectborderwidth=0
        )
        self.gifts_area.pack(fill='both', expand=True)
        
        # تخصيص شريط التمرير للهدايا
        gifts_scrollbar = self.gifts_area.vbar
        gifts_scrollbar.configure(
            bg=self.colors['bg_surface'],
            troughcolor=self.colors['bg_secondary'],
            activebackground=self.colors['accent_green'],
            borderwidth=0,
            highlightthickness=0
        )
        
        # إطار إعدادات قراءة التعليقات
        tts_frame = ttk.LabelFrame(main_frame, text="قراءة التعليقات", style='Modern.TLabelframe')
        tts_frame.pack(fill='x', padx=5, pady=5)
        
        # زر تفعيل/تعطيل قراءة التعليقات
        self.tts_button = ModernButton(
            tts_frame,
            text="تفعيل القراءة",
            style_name='Success.TButton',
            command=self.toggle_tts
        )
        self.tts_button.pack(side='left', padx=5)
        
        # قائمة اختيار اللغة
        self.language_var = tk.StringVar(value='ar')
        language_frame = ttk.Frame(tts_frame)
        language_frame.pack(side='left', padx=5)
        
        ttk.Radiobutton(
            language_frame,
            text="العربية",
            variable=self.language_var,
            value='ar',
            command=self.update_tts_language
        ).pack(side='left')
        
        ttk.Radiobutton(
            language_frame,
            text="الإنجليزية",
            variable=self.language_var,
            value='en',
            command=self.update_tts_language
        ).pack(side='left')
        
        # إطار اختيار الصوت
        voice_frame = ttk.Frame(tts_frame)
        voice_frame.pack(side='left', padx=5)
        
        ttk.Label(voice_frame, text="اختر الصوت:").pack(side='left', padx=2)
        
        # قائمة منسدلة لاختيار الصوت
        self.voice_menu = ttk.Combobox(
            voice_frame,
            textvariable=self.selected_voice,
            state='readonly',
            width=30
        )
        self.update_voice_list()  # تحديث قائمة الأصوات
        self.voice_menu.pack(side='left', padx=2)
        
        # إطار السجل
        log_frame = ttk.LabelFrame(main_frame, text="السجل", style='Custom.TLabelframe')
        log_frame.pack(fill='x', padx=5, pady=5)
        
        # عرض السجل
        self.log_text = tk.Text(log_frame, height=5, bg='#2d2d2d', fg='white', 
                               highlightthickness=0, selectborderwidth=0)
        self.log_text.pack(fill='x', padx=5, pady=5)
        
        # إضافة شريط التمرير للسجل
        log_scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        log_scrollbar.pack(side='right', fill='y')
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        # تطبيق النمط على السجل وإزالة التظليل
        self.log_text.tag_configure('success', foreground='#4CAF50')
        self.log_text.tag_configure('error', foreground='#F44336')
        self.log_text.tag_configure('warning', foreground='#FFC107')
        self.log_text.tag_configure('info', foreground='#2196F3')
        self.log_text.config(highlightthickness=0, selectborderwidth=0)
        
        self.update_keys_display()

    def add_key_binding(self):
        """إضافة محاكاة مفتاح جديدة"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة محاكاة مفتاح")
        dialog.geometry("600x650")  # زيادة الارتفاع لاستيعاب الخيارات الجديدة
        dialog.resizable(False, False)
        dialog.configure(bg='#1e1e1e')  # إضافة خلفية داكنة

        # تطبيق النمط
        style = ttk.Style()
        style.configure("Dialog.TFrame", background='#1e1e1e')
        
        # إزالة التظليل من جميع عناصر النافذة
        style.configure("Dialog.TLabel", background='#1e1e1e', foreground='white', font=('Segoe UI', 10), focuscolor='none', highlightthickness=0)
        style.configure("Dialog.TRadiobutton", background='#1e1e1e', foreground='white', font=('Segoe UI', 10), focuscolor='none', highlightthickness=0)
        style.configure("Dialog.TEntry", fieldbackground='#2d2d2d', foreground='white', font=('Segoe UI', 10), focuscolor='none', highlightthickness=0, borderwidth=1, relief='solid')
        style.configure("Dialog.TLabelframe", background='#1e1e1e', foreground='white', relief='flat', borderwidth=0, focuscolor='none')
        style.configure("Dialog.TLabelframe.Label", background='#1e1e1e', foreground='white', focuscolor='none')
        style.configure("Dialog.TCombobox", 
            fieldbackground='#2d2d2d', 
            foreground='white', 
            focuscolor='none', 
            highlightthickness=0, 
            borderwidth=2, 
            relief='flat',
            arrowcolor='#238be6',
            selectbackground='#238be6',
            selectforeground='white',
            insertcolor='#238be6'
        )
        style.configure("Dialog.TButton", focuscolor='none', highlightthickness=0)
        
        main_frame = ttk.Frame(dialog, style="Dialog.TFrame", padding="10")
        main_frame.pack(fill='both', expand=True)

        # إطار اختيار نوع الحدث
        event_type_frame = ttk.LabelFrame(main_frame, text="نوع الحدث", style="Dialog.TLabelframe", padding=10)
        event_type_frame.pack(fill='x', pady=(0, 10))
        
        # متغير لتخزين نوع الحدث المحدد
        event_type_var = tk.StringVar(value="gift")
        
        # خيارات أنواع الأحداث
        ttk.Radiobutton(event_type_frame, text="هدية", variable=event_type_var, value="gift", style="Dialog.TRadiobutton").pack(side='left', padx=10)
        ttk.Radiobutton(event_type_frame, text="تعليق", variable=event_type_var, value="comment", style="Dialog.TRadiobutton").pack(side='left', padx=10)
        ttk.Radiobutton(event_type_frame, text="متابعة", variable=event_type_var, value="follow", style="Dialog.TRadiobutton").pack(side='left', padx=10)
        ttk.Radiobutton(event_type_frame, text="لايك", variable=event_type_var, value="like", style="Dialog.TRadiobutton").pack(side='left', padx=10)
        ttk.Radiobutton(event_type_frame, text="مشاركة", variable=event_type_var, value="share", style="Dialog.TRadiobutton").pack(side='left', padx=10)
        
        # إطار إعداد عدد اللايكات (يظهر فقط عند اختيار لايك)
        like_count_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        like_count_frame.pack(fill='x', pady=(0, 10))
        like_count_frame.pack_forget()  # إخفاء الإطار بشكل افتراضي
        
        ttk.Label(like_count_frame, text="عدد اللايكات المطلوبة:", style="Dialog.TLabel").pack(side='left', padx=5)
        like_count_var = tk.StringVar(value="10")
        like_count_entry = ttk.Entry(like_count_frame, textvariable=like_count_var, style="Dialog.TEntry", width=10)
        like_count_entry.pack(side='left', padx=5)
        
        # دالة لإظهار/إخفاء إطارات الهدية واللايكات والتعليقات بناءً على نوع الحدث المحدد
        def toggle_frames():
            if event_type_var.get() == "gift":
                gift_frame.pack(fill='x', pady=(0, 10), after=event_type_frame)
                like_count_frame.pack_forget()
                comment_frame.pack_forget()
            elif event_type_var.get() == "like":
                gift_frame.pack_forget()
                like_count_frame.pack(fill='x', pady=(0, 10), after=event_type_frame)
                comment_frame.pack_forget()
            elif event_type_var.get() == "comment":
                gift_frame.pack_forget()
                like_count_frame.pack_forget()
                comment_frame.pack(fill='x', pady=(0, 10), after=event_type_frame)
            else:
                gift_frame.pack_forget()
                like_count_frame.pack_forget()
                comment_frame.pack_forget()
                
        # ربط الدالة بتغير نوع الحدث
        event_type_var.trace_add("write", lambda *args: toggle_frames())
        
        # إطار اختيار الهدية (يظهر فقط عند اختيار هدية)
        gift_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        gift_frame.pack(fill='x', pady=(0, 10))
        gift_frame.pack()  # إظهار الإطار بشكل افتراضي

        # إعداد قائمة الأسماء والأيدي
        gift_names = [gift['name'] for gift in self.gifts_data.values()]
        gift_ids = list(self.gifts_data.keys())

        # القائمة المنسدلة للهدايا مع الصور
        ttk.Label(gift_frame, text="الهدية:", style="Dialog.TLabel").pack(side='left', padx=5)
        
        # إنشاء إطار للقائمة المنسدلة مع الصورة
        combo_frame = ttk.Frame(gift_frame, style="Dialog.TFrame")
        combo_frame.pack(side='left', padx=5, fill='x', expand=True)
        
        # إنشاء قائمة الهدايا مع الصور
        self.gift_combobox = ttk.Combobox(combo_frame, values=self._get_gift_display_names(), state='readonly', width=15, style='Modern.TCombobox')
        self.gift_combobox.pack(side='left', fill='x', expand=True)
        self.gift_combobox.configure(font=('Segoe UI', 14))  # تكبير حجم الخط في القائمة المنسدلة
        
        # تسمية لعرض صورة الهدية المختارة
        self.gift_image_label = ttk.Label(combo_frame, text="🎁", style="Dialog.TLabel", font=('Segoe UI', 36))  # تكبير حجم الصورة
        self.gift_image_label.pack(side='right', padx=(5, 0))
        
        if self.gifts_data:
            gift_names = list(self.gifts_data.values())
            if gift_names:
                # الحصول على اسم الهدية مع عدد الكوينات
                first_gift = gift_names[0]
                coin_count = first_gift.get('diamond_count', 0)
                display_name = f"{first_gift['name']} ({coin_count})"
                self.gift_combobox.set(display_name)  # اختيار أول هدية بشكل افتراضي
                self._update_gift_image()  # تحديث صورة الهدية
            
        # ربط حدث تغيير الاختيار
        self.gift_combobox.bind('<<ComboboxSelected>>', lambda e: self._update_gift_image())
            
        # دالة لإظهار/إخفاء إطار الهدية بناءً على نوع الحدث المحدد
        def toggle_gift_frame():
            if event_type_var.get() == "gift":
                gift_frame.pack(fill='x', pady=(0, 10), after=event_type_frame)
            else:
                gift_frame.pack_forget()
                
        # ربط الدالة بتغير نوع الحدث
        event_type_var.trace_add("write", lambda *args: toggle_gift_frame())

        # إطار التعليق (يظهر فقط عند اختيار تعليق)
        comment_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        comment_frame.pack(fill='x', pady=(0, 10))
        comment_frame.pack_forget()  # إخفاء الإطار بشكل افتراضي

        ttk.Label(comment_frame, text="نص التعليق:", style="Dialog.TLabel").pack(side='left', padx=5)
        comment_text_var = tk.StringVar(value="شكراً على تعليقك!")
        comment_entry = ttk.Entry(comment_frame, textvariable=comment_text_var, style="Dialog.TEntry")
        comment_entry.pack(side='left', padx=5, fill='x', expand=True)

        # حقل الاسم
        ttk.Label(main_frame, text="الاسم:", style="Dialog.TLabel").pack(anchor='w', pady=5)
        name_entry = ttk.Entry(main_frame, style="Dialog.TEntry")
        name_entry.pack(fill='x', padx=20)

        # إطار تعليمات تنسيق المفاتيح
        help_frame = ttk.LabelFrame(main_frame, text="تنسيق المفاتيح:", style="Dialog.TLabelframe", padding=10)
        help_frame.pack(fill='x', pady=10)
        
        help_text = """• استخدم + للجمع بين المفاتيح: ctrl+shift+a
• استخدم @ للتأخير: a@200 (200 مللي ثانية)
• استخدم ; للفصل بين التسلسلات: a@100;b@200
• المفاتيح الخاصة: space, backspace, enter, tab, esc
  up, down, left, right
• المعدلات: ctrl, alt, shift"""
        
        help_label = ttk.Label(help_frame, text=help_text, style="Dialog.TLabel", justify='right')
        help_label.pack(anchor='e')  # تغيير المحاذاة إلى اليمين
        
        
        # المفاتيح
        ttk.Label(main_frame, text="المفاتيح:", style="Dialog.TLabel").pack(anchor='w', pady=5)
        keys_entry = ttk.Entry(main_frame, style="Dialog.TEntry")
        keys_entry.pack(fill='x', padx=20)

        # الصوت
        sound_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        sound_frame.pack(fill='x', pady=(10, 5))
        ttk.Label(sound_frame, text="الصوت:", style="Dialog.TLabel").pack(side='left', padx=5)
        sound_var = tk.StringVar()
        sound_entry = ttk.Entry(sound_frame, textvariable=sound_var, style="Dialog.TEntry", width=30)
        sound_entry.pack(side='left', fill='x', expand=True)
        browse_button = ModernButton(sound_frame, text="📂", width=60, height=30,
                                   command=lambda: self.browse_sound_file(sound_var), style_name='Modern.TButton')
        browse_button.pack(side='right', padx=5)

        def test_keys():
            keys = keys_entry.get().strip()
            if not keys:
                self.log_comment("❌ الرجاء إدخال المفاتيح!")
                return
            
            success, message = self.test_keys(keys)
            self.log_comment(message)
            
        def test_sound():
            sound = sound_var.get().strip()
            if not sound:
                self.log_comment("❌ الرجاء اختيار ملف صوتي!")
                return
                
            success, message = self.test_sound(sound)
            self.log_comment(message)

        def save():
            event_type = event_type_var.get()
            name = name_entry.get().strip()
            if not name:
                self.log_comment("❌ الرجاء إدخال الاسم!")
                return

            keys = keys_entry.get().strip()
            if not keys:
                self.log_comment("❌ الرجاء إدخال المفاتيح!")
                return
                
            # تحديد معرف الحدث بناءً على نوعه
            event_id = None
            event_name = ""
            
            if event_type == "gift":
                selected_gift_name = self.gift_combobox.get().strip()
                if not selected_gift_name:
                    self.log_comment("❌ الرجاء اختيار هدية!")
                    return
                    
                # الحصول على معرف الهدية المحددة
                event_id = None
                # استخراج اسم الهدية من النص المعروض (إزالة عدد الكوينات)
                gift_name_only = selected_gift_name
                if " (" in selected_gift_name and ")" in selected_gift_name:
                    gift_name_only = selected_gift_name.split(" (")[0]
                
                for gid, gift in self.gifts_data.items():
                    if gift['name'] == gift_name_only:
                        event_id = gid
                        break
                
                if event_id is None:
                    self.log_comment("❌ لم يتم العثور على معرف الهدية!")
                    return
                event_name = selected_gift_name
            elif event_type == "follow":
                event_id = "follow_event"
                event_name = "متابعة جديدة"
            elif event_type == "like":
                event_id = "like_event"
                event_name = "لايك جديد"
            elif event_type == "comment":
                event_id = "comment_event"
                event_name = "تعليق جديد"
            elif event_type == "share":
                event_id = "share_event"
                event_name = "مشاركة جديدة"

            # تحديث القاموس
            binding_data = {
                'custom_name': name,
                'keys': keys,
                'sound': sound_var.get(),
                'event_type': event_type  # إضافة نوع الحدث
            }
            
            # إضافة عدد اللايكات المطلوبة إذا كان الحدث من نوع لايك
            if event_type == "like":
                try:
                    like_count = int(like_count_var.get())
                    if like_count <= 0:
                        like_count = 10  # القيمة الافتراضية إذا كان الإدخال غير صالح
                    binding_data['likes_count'] = like_count
                except ValueError:
                    binding_data['likes_count'] = 10  # القيمة الافتراضية إذا كان الإدخال غير صالح
            
            # إضافة نص التعليق إذا كان الحدث من نوع تعليق
            if event_type == "comment":
                comment_text = comment_text_var.get().strip()
                if not comment_text:
                    comment_text = "شكراً على تعليقك!"  # النص الافتراضي
                binding_data['comment_text'] = comment_text
            
            self.key_bindings[event_id] = binding_data

            # حفظ التغييرات في الملف
            self.save_key_bindings()

            # إغلاق النافذة
            dialog.destroy()
            self.log_comment(f"✅ تم حفظ محاكاة المفتاح للحدث: {event_name} (ID: {event_id})")

        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        buttons_frame.pack(fill='x', pady=5)  # تقليل المسافة العلوية
        
        # استخدام ModernButton بدلاً من ttk.Button
        test_keys_btn = ModernButton(buttons_frame, text="🔍 اختبار المفاتيح", command=test_keys, style_name='Modern.TButton')
        test_keys_btn.pack(side='left', padx=5)
        
        test_sound_btn = ModernButton(buttons_frame, text="🔊 اختبار الصوت", command=test_sound, style_name='Modern.TButton')
        test_sound_btn.pack(side='left', padx=5)
        
        # إطار للأزرار السفلية
        bottom_buttons_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        bottom_buttons_frame.pack(fill='x', pady=(5, 15))  # زيادة المسافة السفلية

        cancel_btn = ModernButton(bottom_buttons_frame, text="❌ إلغاء", command=dialog.destroy, style_name='Danger.TButton')
        cancel_btn.pack(side='right', padx=5)
        
        save_button = ModernButton(bottom_buttons_frame, text="💾 حفظ", command=save, style_name='Success.TButton')
        save_button.pack(side='right', padx=5)
        
        # جعل النافذة مركزية
        dialog.transient(self.root)
        dialog.grab_set()
        self.root.wait_window(dialog)
    
    def toggle_connection(self):
        if not self.is_connected:
            self.connect_to_live()
        else:
            self.disconnect_from_live()
    
    def connect_to_live(self):
        username = self.username_var.get().strip()
        if not username:
            self.log_comment("الرجاء إدخال اسم مستخدم TikTok")
            return
        
        if not username.startswith("@"):
            username = f"@{username}"
        
        self.status_var.set("الحالة: جاري الاتصال...")
        self.connect_btn.configure(state="disabled")
        
        threading.Thread(target=self.start_client, args=(username,), daemon=True).start()
    
    def disconnect_from_live(self):
        if self.client:
            try:
                # تعطيل الزر وتحديث الحالة
                self.connect_btn.configure(state="disabled", text="جاري قطع الاتصال...")
                self.status_var.set("الحالة: جاري قطع الاتصال...")
                
                # إيقاف جميع المستمعين
                self.client.remove_all_listeners()
                
                # إيقاف اتصال WebSocket
                if hasattr(self.client, '_websocket') and self.client._websocket:
                    try:
                        self.client._websocket.close()
                    except:
                        pass
                
                # إيقاف العميل وتنظيف الموارد
                if hasattr(self.client, '_room'):
                    self.client._room = None
                if hasattr(self.client, '_connect_id'):
                    self.client._connect_id = None
                
                # تعيين حالة التوقف
                self.client._stopped = True
                self.client = None
                self.is_connected = False
                
                # تحديث الواجهة
                self.status_var.set("الحالة: تم قطع الاتصال")
                self.connect_btn.configure(text="اتصال", state="normal")
                self.log_comment("تم قطع الاتصال بنجاح")
                
                # تشغيل صوت قطع الاتصال
                if os.path.exists(self.disconnect_sound):
                    threading.Thread(target=playsound, args=(self.disconnect_sound,), daemon=True).start()
                
            except Exception as e:
                self.log_comment(f"خطأ في قطع الاتصال: {str(e)}")
                self.connect_btn.configure(text="اتصال", state="normal")
                self.status_var.set("الحالة: غير متصل")
                self.is_connected = False

    def start_client(self, username):
        try:
            self.client = TikTokLiveClient(unique_id=username)
            
            @self.client.on(ConnectEvent)
            async def on_connect(event):
                self.is_connected = True
                self.root.after(0, lambda: [
                    self.status_var.set(f"الحالة: متصل بـ {event.unique_id}"),
                    self.connect_btn.configure(text="قطع الاتصال", state="normal"),
                    self.log_comment(f"تم الاتصال بـ {event.unique_id}!")
                ])
                
                # تشغيل صوت الاتصال
                if os.path.exists(self.connect_sound):
                    threading.Thread(target=playsound, args=(self.connect_sound,), daemon=True).start()
            
            @self.client.on(DisconnectEvent)
            async def on_disconnect(_):
                self.root.after(0, lambda: [
                    self.status_var.set("الحالة: تم قطع الاتصال"),
                    self.connect_btn.configure(text="اتصال", state="normal"),
                    self.log_comment("تم قطع الاتصال من الخادم")
                ])
                self.is_connected = False
                
                # محاولة إعادة الاتصال تلقائياً
                if self.reconnect_attempts < self.max_reconnect_attempts:
                    self.reconnect_attempts += 1
                    self.log_comment(f"محاولة إعادة الاتصال {self.reconnect_attempts}/{self.max_reconnect_attempts}")
                    
                    # تشغيل صوت فشل الاتصال
                    if os.path.exists(self.connection_error_sound):
                        threading.Thread(target=playsound, args=(self.connection_error_sound,), daemon=True).start()
                    
                    # محاولة إعادة الاتصال بعد 5 ثوانٍ
                    self.root.after(5000, lambda: self.toggle_connection())
                else:
                    self.reconnect_attempts = 0
                    self.log_comment("فشلت جميع محاولات إعادة الاتصال")
                
                # تشغيل صوت قطع الاتصال
                if os.path.exists(self.disconnect_sound):
                    threading.Thread(target=playsound, args=(self.disconnect_sound,), daemon=True).start()
            
            @self.client.on(CommentEvent)
            async def on_comment(event):
                try:
                    # تنسيق رسالة التعليق
                    current_time = datetime.now().strftime("%H:%M:%S")
                    
                    # استخدام طريقة أكثر أمانًا للوصول إلى خصائص الكائن
                    # محاولة الوصول إلى خصائص مختلفة لاسم المستخدم
                    user_id = "مستخدم غير معروف"
                    
                    # محاولة الحصول على معلومات المستخدم من user_info
                    if hasattr(event, "user_info") and event.user_info:
                        user_id = getattr(event.user_info, "unique_id", None) or getattr(event.user_info, "nickname", None) or getattr(event.user_info, "username", None) or getattr(event.user_info, "display_name", None)
                    
                    # محاولة الحصول على معلومات المستخدم من user_identity
                    if user_id == "مستخدم غير معروف" and hasattr(event, "user_identity") and event.user_identity:
                        user_id = getattr(event.user_identity, "unique_id", None) or getattr(event.user_identity, "nickname", None) or getattr(event.user_identity, "username", None) or getattr(event.user_identity, "display_name", None)
                    
                    # محاولة الحصول على معلومات المستخدم من خصائص أخرى
                    if user_id == "مستخدم غير معروف":
                        user_id = getattr(event, "unique_id", None) or getattr(event, "nickname", None) or getattr(event, "username", None) or getattr(event, "display_name", None)
                    
                    # إذا لم يتم العثور على أي معرف، استخدم القيمة الافتراضية
                    if not user_id:
                        user_id = "مستخدم غير معروف"
                    
                    comment_text = getattr(event, "comment", "تعليق غير متوفر")
                    message = f"[{current_time}] 💭 {user_id}: {comment_text}"
                    
                    # إضافة التعليق للعرض
                    self.comments_queue.put(message)
                    
                    # تفعيل قراءة الصوت إذا كانت مفعلة
                    if self.tts_enabled:
                        try:
                            if self.tts_language == 'ar':
                                text_to_speak = f"{user_id} يقول: {comment_text}"
                            else:
                                text_to_speak = f"{user_id} says: {comment_text}"
                            
                            # إضافة التعليق لقائمة القراءة
                            self.tts_queue.put(text_to_speak)
                        except Exception as e:
                            print(f"خطأ في TTS: {str(e)}")
                    
                    # التحقق من وجود حدث تعليق محفوظ وتنفيذه
                    if "comment_event" in self.key_bindings:
                        saved_comment_text = self.key_bindings["comment_event"].get("comment_text", "")
                        
                        # مقارنة النص (إزالة المسافات الزائدة وتجاهل حساسية الأحرف)
                        if saved_comment_text.strip().lower() == comment_text.strip().lower():
                            # تنفيذ الحدث
                            self.root.after(100, lambda: self.simulate_keys("comment_event"))
                            
                            # تسجيل تنفيذ الحدث
                            execute_message = f"[{current_time}] 🎯 تم تنفيذ حدث التعليق! النص المطابق: '{comment_text}'"
                            self.comments_queue.put(execute_message)
                        
                except Exception as e:
                    self.log_comment(f"خطأ في معالجة التعليق: {str(e)}")
            
            @self.client.on(GiftEvent)
            async def on_gift(event):
                try:
                    # استخدام الخصائص المكتشفة من التشخيص
                    gift = None
                    gift_id = None
                    gift_name = "هدية غير معروفة"
                    
                    # محاولة الوصول للهدية بالطرق المتاحة
                    if hasattr(event, 'm_gift') and event.m_gift:
                        # استخدام m_gift (الخاصية الصحيحة)
                        gift = event.m_gift
                        gift_id = str(getattr(gift, 'id', getattr(gift, 'gift_id', 'unknown')))
                        gift_name = getattr(gift, 'name', getattr(gift, 'display_name', gift_name))
                    elif hasattr(event, 'gifts_in_box') and event.gifts_in_box:
                        # محاولة استخدام gifts_in_box
                        gifts_box = event.gifts_in_box
                        if len(gifts_box) > 0:
                            gift = gifts_box[0]  # أخذ أول هدية
                            gift_id = str(getattr(gift, 'id', getattr(gift, 'gift_id', 'unknown')))
                            gift_name = getattr(gift, 'name', getattr(gift, 'display_name', gift_name))
                    elif hasattr(event, 'gift_monitor_info') and event.gift_monitor_info:
                        # محاولة استخدام gift_monitor_info
                        gift_info = event.gift_monitor_info
                        gift_id = str(getattr(gift_info, 'gift_id', 'unknown'))
                    
                    # إذا لم نحصل على معرف، نحاول استخراجه من البيانات الأساسية
                    if not gift_id or gift_id == 'unknown':
                        # محاولة استخدام to_dict لاستخراج البيانات
                        try:
                            event_dict = event.to_dict()
                            if 'gift' in event_dict:
                                gift_data = event_dict['gift']
                                gift_id = str(gift_data.get('id', gift_data.get('gift_id', 'unknown')))
                                gift_name = gift_data.get('name', gift_data.get('display_name', gift_name))
                        except:
                            pass
                    
                    if not gift_id or gift_id == 'unknown':
                        # لم يتم العثور على معرف الهدية
                        return
                    
                    # الحصول على اسم الهدية
                    if gift:
                        gift_name = getattr(gift, 'name', gift_name)
                        if gift_name == "هدية غير معروفة" and hasattr(gift, 'display_name'):
                            gift_name = gift.display_name
                    
                    # البحث في بيانات الهدايا المحملة
                    if gift_id in self.gifts_data:
                        gift_name = self.gifts_data[gift_id].get('name', gift_name)
                    
                    # تحديد عدد الهدايا
                    gift_count = 1
                    
                    # التحقق من نوع الهدية وإمكانية تكرارها
                    if gift and hasattr(gift, 'streakable'):
                        # هدية قابلة للتكرار - ننتظر حتى تنتهي السلسلة
                        if gift.streakable and hasattr(event, 'streaking') and event.streaking:
                            return  # تجاهل الهدايا المتوسطة في السلسلة
                        
                        # نهاية السلسلة أو هدية فردية
                        gift_count = getattr(event, 'repeat_count', getattr(event, 'count', 1))
                    elif gift and hasattr(gift, 'info'):
                        # التعامل مع البروتوكول القديم
                        if gift.info.type == 1:
                            if not hasattr(event, 'repeat_end') or not event.repeat_end:
                                return  # تجاهل الهدايا المتوسطة في السلسلة
                            gift_count = getattr(gift, 'repeat_count', getattr(event, 'repeat_count', 1))
                    else:
                        # محاولة الحصول على العدد من الحدث مباشرة
                        gift_count = getattr(event, 'count', getattr(event, 'repeat_count', 1))
                    
                    # الحصول على الوقت الحالي
                    current_time = datetime.now().strftime("%H:%M:%S")
                    
                    # استخراج اسم المستخدم من from_user أولاً (المصدر الصحيح)
                    user_name = "مستخدم غير معروف"
                    if hasattr(event, 'from_user') and event.from_user:
                        from_user = event.from_user
                        user_name = (
                            getattr(from_user, 'nick_name', None) or
                            getattr(from_user, 'username', None) or
                            getattr(from_user, 'unique_id', None) or
                            getattr(from_user, 'display_name', None) or
                            str(getattr(from_user, 'id', ''))
                        )
                        # إزالة المسافات والتحقق من عدم وجود قيم فارغة
                        if user_name and user_name.strip() and user_name.strip() != '0':
                            user_name = user_name.strip()
                        else:
                            user_name = "مستخدم غير معروف"
                    
                    
                    # محاولة استخراج المعلومات من m_gift إذا فشلت الطرق التقليدية
                    if user_name == "مستخدم غير معروف" and hasattr(event, 'm_gift') and event.m_gift:
                        if hasattr(event.m_gift, 'sender_user') and event.m_gift.sender_user:
                            sender = event.m_gift.sender_user
                            user_name = getattr(sender, 'nickname', getattr(sender, 'unique_id', getattr(sender, 'display_id', user_name)))
                        elif hasattr(event.m_gift, 'describe_message') and event.m_gift.describe_message:
                            # استخراج اسم المستخدم من الرسالة الوصفية
                            user_name = event.m_gift.describe_message.split(' ')[0]
                    
                    # التحقق من وجود معرف الهدية في الإعدادات
                    if gift_id in self.key_bindings:
                        # تسجيل الهدية في منطقة الهدايا
                        
                        message = f"[{current_time}] 🎁 {user_name} أرسل {gift_name} × {gift_count}"
                        self.gifts_queue.put(message)

                        # تنفيذ الأمر لكل هدية في السلسلة
                        for _ in range(gift_count):
                            self.root.after(100, lambda: self.simulate_keys(gift_id))
                    else:
                        # تسجيل الهدية غير المعيّنة لأغراض التتبع (استخدام نفس user_name المُستخرج مسبقاً)
                        debug_message = f"[{current_time}] 🎁 [غير معيّن] {user_name} أرسل {gift_name} (ID: {gift_id}) × {gift_count}"
                        self.gifts_queue.put(debug_message)
                
                except Exception as e:
                    error_message = f"❌ خطأ في معالجة الهدية: {str(e)}"
                    self.gifts_queue.put(error_message)
                    
                    # إضافة معلومات تشخيصية إضافية
                    try:
                        available_attrs = [attr for attr in dir(event) if not attr.startswith('_')]
                        debug_message = f"[DEBUG] خصائص الحدث المتاحة: {available_attrs[:10]}..."
                        self.gifts_queue.put(debug_message)
                    except:
                        pass
            
            # استيراد أنواع الأحداث الإضافية
            try:
                from TikTokLive.events import FollowEvent, LikeEvent, ShareEvent
                
                @self.client.on(FollowEvent)
                async def on_follow(event):
                    try:
                        # التحقق من وجود إعدادات للمتابعة
                        if "follow_event" in self.key_bindings:
                            # تسجيل المتابعة في منطقة التعليقات
                            current_time = datetime.now().strftime("%H:%M:%S")
                            
                            # محاولة الحصول على اسم المستخدم
                            user_id = "مستخدم غير معروف"
                            if hasattr(event, "user") and event.user:
                                user_id = getattr(event.user, "unique_id", None) or getattr(event.user, "nickname", None) or getattr(event.user, "username", None) or getattr(event.user, "display_name", None)
                            
                            if user_id == "مستخدم غير معروف":
                                user_id = getattr(event, "unique_id", None) or getattr(event, "nickname", None) or getattr(event, "username", None) or getattr(event, "display_name", None)
                            
                            if not user_id:
                                user_id = "مستخدم غير معروف"
                                
                            message = f"[{current_time}] 👤 {user_id} قام بمتابعتك"
                            self.comments_queue.put(message)
                            
                            # تنفيذ الأمر
                            self.root.after(100, lambda: self.simulate_keys("follow_event"))
                    except Exception as e:
                        error_message = f"❌ خطأ في معالجة المتابعة: {str(e)}"
                        self.comments_queue.put(error_message)
                
                # متغيرات لتخزين عدد اللايكات لكل مستخدم
                if not hasattr(self, "user_like_counters"):
                    self.user_like_counters = {}  # قاموس لحفظ عداد كل مستخدم
                    self.user_reset_timers = {}  # مؤقتات إعادة تعيين لكل مستخدم
                
                @self.client.on(LikeEvent)
                async def on_like(event):
                    try:
                        # التحقق من وجود إعدادات للايك
                        if "like_event" in self.key_bindings:
                            # الحصول على عدد اللايكات المطلوب من الإعدادات
                            required_likes = self.key_bindings["like_event"].get("likes_count", 10)
                            
                            # محاولة الحصول على اسم المستخدم
                            user_id = "مستخدم غير معروف"
                            if hasattr(event, "user") and event.user:
                                user_id = getattr(event.user, "unique_id", None) or getattr(event.user, "nickname", None) or getattr(event.user, "username", None) or getattr(event.user, "display_name", None)
                            
                            if user_id == "مستخدم غير معروف":
                                user_id = getattr(event, "unique_id", None) or getattr(event, "nickname", None) or getattr(event, "username", None) or getattr(event, "display_name", None)
                            
                            if not user_id:
                                user_id = "مستخدم غير معروف"
                            
                            # الحصول على عدد اللايكات إذا كان متوفرًا
                            like_count = getattr(event, "count", 1)
                            
                            # تسجيل اللايك في منطقة التعليقات
                            current_time = datetime.now().strftime("%H:%M:%S")
                            
                            # إضافة اللايكات لعداد المستخدم
                            if user_id not in self.user_like_counters:
                                self.user_like_counters[user_id] = 0
                            
                            self.user_like_counters[user_id] += like_count
                            user_total_likes = self.user_like_counters[user_id]
                            
                            message = f"[{current_time}] ❤️ {user_id} أرسل {like_count} لايك (المجموع: {user_total_likes})"
                            self.comments_queue.put(message)
                            
                            # إلغاء المؤقت السابق للمستخدم وإنشاء مؤقت جديد
                            if user_id in self.user_reset_timers:
                                self.root.after_cancel(self.user_reset_timers[user_id])
                            
                            def reset_user_counter():
                                if user_id in self.user_like_counters and self.user_like_counters[user_id] > 0:
                                    reset_message = f"⏰ تم إعادة تعيين عداد {user_id} بعد انتهاء المهلة الزمنية"
                                    self.comments_queue.put(reset_message)
                                    del self.user_like_counters[user_id]
                                if user_id in self.user_reset_timers:
                                    del self.user_reset_timers[user_id]
                            
                            self.user_reset_timers[user_id] = self.root.after(60000, reset_user_counter)  # 60 ثانية لكل مستخدم
                            
                            # التحقق من وصول هذا المستخدم للعدد المطلوب
                            if user_total_likes >= required_likes:
                                # إلغاء المؤقت للمستخدم
                                if user_id in self.user_reset_timers:
                                    self.root.after_cancel(self.user_reset_timers[user_id])
                                    del self.user_reset_timers[user_id]
                                
                                # تنفيذ الأمر
                                self.root.after(100, lambda: self.simulate_keys("like_event"))
                                
                                # تسجيل وصول العدد المطلوب
                                complete_message = f"[{current_time}] 🎯 {user_id} وصل إلى {user_total_likes} لايك! تم تنفيذ الأمر."
                                self.comments_queue.put(complete_message)
                                
                                # إعادة تعيين عداد هذا المستخدم فقط
                                del self.user_like_counters[user_id]
                            else:
                                # عرض التقدم للمستخدم
                                progress_message = f"[{current_time}] 📊 {user_id}: {user_total_likes}/{required_likes} لايك"
                                self.comments_queue.put(progress_message)
                    except Exception as e:
                        error_message = f"❌ خطأ في معالجة اللايك: {str(e)}"
                        self.comments_queue.put(error_message)
                
                @self.client.on(ShareEvent)
                async def on_share(event):
                    try:
                        # التحقق من وجود إعدادات للمشاركة
                        if "share_event" in self.key_bindings:
                            # تسجيل المشاركة في منطقة التعليقات
                            current_time = datetime.now().strftime("%H:%M:%S")
                            
                            # محاولة الحصول على اسم المستخدم
                            user_id = "مستخدم غير معروف"
                            if hasattr(event, "user") and event.user:
                                user_id = getattr(event.user, "unique_id", None) or getattr(event.user, "nickname", None) or getattr(event.user, "username", None) or getattr(event.user, "display_name", None)
                            
                            if user_id == "مستخدم غير معروف":
                                user_id = getattr(event, "unique_id", None) or getattr(event, "nickname", None) or getattr(event, "username", None) or getattr(event, "display_name", None)
                            
                            if not user_id:
                                user_id = "مستخدم غير معروف"
                                
                            message = f"[{current_time}] 🔄 {user_id} قام بمشاركة البث"
                            self.comments_queue.put(message)
                            
                            # تنفيذ الأمر
                            self.root.after(100, lambda: self.simulate_keys("share_event"))
                    except Exception as e:
                        error_message = f"❌ خطأ في معالجة المشاركة: {str(e)}"
                        self.comments_queue.put(error_message)
                        
            except ImportError:
                # إذا لم تكن أنواع الأحداث متوفرة في الإصدار الحالي من المكتبة
                self.log_comment("⚠️ أنواع الأحداث الإضافية غير متوفرة في الإصدار الحالي من المكتبة")

            self.client.run()
            
        except Exception as e:
            self.is_connected = False
            self.reconnect_attempts += 1  # زيادة عدد المحاولات
            error_message = f"فشل الاتصال: {str(e)} (محاولة رقم {self.reconnect_attempts})"
            
            self.root.after(0, lambda: [
                self.status_var.set("الحالة: فشل الاتصال"),
                self.connect_btn.configure(state="normal"),
                self.log_comment(error_message)
            ])

            # تشغيل صوت فشل الاتصال إن وجد
            if os.path.exists(self.connection_error_sound):
                threading.Thread(target=playsound, args=(self.connection_error_sound,), daemon=True).start()

            # محاولة إعادة الاتصال بعد 5 ثوانٍ
            self.root.after(5000, lambda: self.connect_to_live())
    
    def save_settings(self):
        """حفظ الإعدادات"""
        if self.current_profile:
            self.profiles[self.current_profile]['bindings'] = self.key_bindings
        self.save_profiles()
    
    def edit_key_binding(self, event=None):
        """تعديل التعيين المحدد"""
        selected_items = self.keys_tree.selection()
        if not selected_items:
            self.log_comment("⚠️ الرجاء تحديد تعيين للتعديل")
            return
            
        # الحصول على معرف التعيين المحدد
        item_id = selected_items[0]
        if item_id not in self.key_bindings:
            self.log_comment("⚠️ التعيين المحدد غير موجود")
            return
            
        # الحصول على بيانات التعيين
        binding = self.key_bindings[item_id]
        
        # إنشاء نافذة التعديل
        dialog = tk.Toplevel(self.root)
        dialog.title("تعديل محاكاة المفتاح")
        dialog.geometry("500x550")
        dialog.configure(bg='#1e1e1e')
        
        # الإطار الرئيسي
        frame = ttk.Frame(dialog, style='Custom.TFrame', padding="20")
        frame.pack(fill='both', expand=True)
        
        # حقل نوع الحدث
        ttk.Label(frame, text="نوع الحدث:", style='Custom.TLabel').pack(fill='x', pady=(0, 5))
        event_type_var = tk.StringVar(value=binding.get('event_type', 'gift'))
        event_type_frame = ttk.Frame(frame, style='Custom.TFrame')
        event_type_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Radiobutton(event_type_frame, text="هدية", variable=event_type_var, value="gift", style='Custom.TRadiobutton').pack(side='left', padx=10)
        ttk.Radiobutton(event_type_frame, text="تعليق", variable=event_type_var, value="comment", style='Custom.TRadiobutton').pack(side='left', padx=10)
        ttk.Radiobutton(event_type_frame, text="إعجاب", variable=event_type_var, value="like", style='Custom.TRadiobutton').pack(side='left', padx=10)
        ttk.Radiobutton(event_type_frame, text="متابع جديد", variable=event_type_var, value="follow", style='Custom.TRadiobutton').pack(side='left', padx=10)
        ttk.Radiobutton(event_type_frame, text="مشاركة", variable=event_type_var, value="share", style='Custom.TRadiobutton').pack(side='left', padx=10)
        
        # حقل الاسم المخصص
        ttk.Label(frame, text="الاسم المخصص:", style='Custom.TLabel').pack(fill='x', pady=(10, 5))
        custom_name_var = tk.StringVar(value=binding.get('custom_name', ''))
        custom_name_entry = ttk.Entry(frame, textvariable=custom_name_var, style='Custom.TEntry')
        custom_name_entry.pack(fill='x', pady=(0, 10))
        
        # إعدادات خاصة بنوع الحدث
        ttk.Label(frame, text="إعدادات الحدث:", style='Custom.TLabel').pack(fill='x', pady=(10, 5))
        event_settings_frame = ttk.Frame(frame, style='Custom.TFrame')
        event_settings_frame.pack(fill='x', pady=(0, 10))
        
        # إعدادات الهدية - تظهر فقط عند تحديد نوع الحدث "هدية"
        gift_frame = ttk.Frame(event_settings_frame, style='Custom.TFrame')
        gift_frame.pack(fill='x')
        
        ttk.Label(gift_frame, text="نوع الهدية:", style='Custom.TLabel').pack(side='left', padx=(0, 5))
        gift_type_var = tk.StringVar(value=binding.get('gift_id', item_id))
        gift_type_combo = ttk.Combobox(gift_frame, textvariable=gift_type_var, style='Custom.TCombobox')
        
        # استخدام الهدايا الحقيقية من self.gifts_data
        gift_display_names = self._get_gift_display_names_with_id()
        gift_type_combo['values'] = gift_display_names
        gift_type_combo.pack(side='left', padx=5, fill='x', expand=True)
        
        # تسمية لعرض صورة الهدية في التعديل
        edit_gift_image_label = ttk.Label(gift_frame, text="🎁", style='Custom.TLabel')
        edit_gift_image_label.pack(side='right', padx=(5, 0))
        
        # دالة تحديث صورة الهدية في التعديل
        def update_edit_gift_image():
            try:
                selected_text = gift_type_combo.get()
                if selected_text and "(ID: " in selected_text:
                    gift_id = selected_text.split("(ID: ")[1].rstrip(")")
                    if gift_id in self.gift_images:
                        edit_gift_image_label.config(image=self.gift_images[gift_id], text="")
                    else:
                        edit_gift_image_label.config(image="", text="🎁")
            except:
                edit_gift_image_label.config(image="", text="🎁")
                
        gift_type_combo.bind('<<ComboboxSelected>>', lambda e: update_edit_gift_image())
        
        # تحديث الصورة الأولية
        if gift_display_names:
            update_edit_gift_image()
        
        # إعدادات الإعجابات - تظهر فقط عند تحديد نوع الحدث "إعجاب"
        likes_frame = ttk.Frame(event_settings_frame, style='Custom.TFrame')
        likes_frame.pack(fill='x')
        
        ttk.Label(likes_frame, text="عدد الإعجابات:", style='Custom.TLabel').pack(side='left', padx=(0, 5))
        likes_count_var = tk.StringVar(value=str(binding.get('likes_count', 1)))
        likes_entry = ttk.Entry(likes_frame, textvariable=likes_count_var, style='Custom.TEntry', width=10)
        likes_entry.pack(side='left')
        
        # إعدادات التعليقات - تظهر فقط عند تحديد نوع الحدث "تعليق"
        comment_frame = ttk.Frame(event_settings_frame, style='Custom.TFrame')
        comment_frame.pack(fill='x')
        
        ttk.Label(comment_frame, text="نص التعليق:", style='Custom.TLabel').pack(side='left', padx=(0, 5))
        comment_text_var = tk.StringVar(value=binding.get('comment_text', 'شكراً على تعليقك!'))
        comment_entry = ttk.Entry(comment_frame, textvariable=comment_text_var, style='Custom.TEntry')
        comment_entry.pack(side='left', padx=5, fill='x', expand=True)
        
        # دالة لتحديث ظهور/اختفاء إعدادات الهدايا
        def update_gifts_visibility():
            if event_type_var.get() == "gift":
                gift_frame.pack(fill='x')
            else:
                gift_frame.pack_forget()
        
        # دالة لتحديث ظهور/اختفاء إعدادات الإعجابات
        def update_likes_visibility():
            if event_type_var.get() == "like":
                # التأكد من أن الإطار معبأ أولاً قبل استخدام after
                if gift_frame.winfo_ismapped():
                    likes_frame.pack(fill='x', after=gift_frame)
                else:
                    likes_frame.pack(fill='x')
            else:
                likes_frame.pack_forget()
        
        # دالة لتحديث ظهور/اختفاء إعدادات التعليقات
        def update_comment_visibility():
            if event_type_var.get() == "comment":
                # التأكد من أن الإطار معبأ أولاً قبل استخدام after
                if gift_frame.winfo_ismapped():
                    comment_frame.pack(fill='x', after=gift_frame)
                elif likes_frame.winfo_ismapped():
                    comment_frame.pack(fill='x', after=likes_frame)
                else:
                    comment_frame.pack(fill='x')
            else:
                comment_frame.pack_forget()
        
        # ربط الدوال بتغير نوع الحدث
        event_type_var.trace_add("write", lambda *args: update_gifts_visibility())
        event_type_var.trace_add("write", lambda *args: update_likes_visibility())
        event_type_var.trace_add("write", lambda *args: update_comment_visibility())
        
        # تحديث الحالة الأولية
        update_gifts_visibility()
        update_likes_visibility()
        update_comment_visibility()
        
        # حقل المفاتيح
        ttk.Label(frame, text="المفاتيح:", style='Custom.TLabel').pack(fill='x', pady=(0, 5))
        keys_entry = ttk.Entry(frame, style='Custom.TEntry')
        keys_entry.insert(0, binding.get('keys', ''))
        keys_entry.pack(fill='x', pady=(0, 10))
        
        # زر اختبار المفاتيح
        test_result_label = ttk.Label(frame, text="", style='Custom.TLabel')
        test_result_label.pack(fill='x', pady=(0, 10))
        
        test_keys_btn = ModernButton(frame, text="🔍 اختبار المفاتيح", 
                                  command=lambda: self.test_keys_entry(keys_entry, test_result_label),
                                  style_name='Modern.TButton')
        test_keys_btn.pack(pady=5)
        
        # حقل الصوت
        sound_frame = ttk.Frame(frame, style='Custom.TFrame')
        sound_frame.pack(fill='x', pady=(10, 5))
        ttk.Label(sound_frame, text="الصوت:", style='Custom.TLabel').pack(side='left', padx=5)
        sound_var = tk.StringVar(value=binding.get('sound', ''))
        sound_entry = ttk.Entry(sound_frame, textvariable=sound_var, style='Custom.TEntry')
        sound_entry.pack(side='left', fill='x', expand=True)
        browse_button = ModernButton(sound_frame, text="📂", width=60, height=30,
                                  command=lambda: self.browse_sound_file(sound_var), style_name='Modern.TButton')
        browse_button.pack(side='right', padx=5)
        
        # زر اختبار الصوت
        test_sound_btn = ModernButton(frame, text="🔊 اختبار الصوت", 
                                   command=lambda: self.test_sound_entry(sound_var),
                                   style_name='Modern.TButton')
        test_sound_btn.pack(pady=5)
        
        # الأزرار
        buttons_frame = ttk.Frame(frame, style='Custom.TFrame')
        buttons_frame.pack(fill='x', pady=(20, 0))
        
        cancel_btn = ModernButton(buttons_frame, text="❌ إلغاء", 
                               command=dialog.destroy, style_name='Danger.TButton')
        cancel_btn.pack(side='right', padx=5)
        
        save_btn = ModernButton(buttons_frame, text="💾 حفظ", 
                             command=lambda: self.save_key_binding_edit(item_id, event_type_var, gift_type_var, 
                                                                   likes_count_var, comment_text_var, keys_entry, 
                                                                   sound_var, custom_name_var, dialog),
                             style_name='Success.TButton')
        save_btn.pack(side='right', padx=5)
        
    def test_keys_entry(self, keys_entry, result_label):
        """اختبار المفاتيح المدخلة"""
        keys = keys_entry.get().strip()
        if not keys:
            result_label.config(text="❌ الرجاء إدخال المفاتيح!", foreground="red")
            return
            
        success, message = self.test_keys(keys)
        if success:
            result_label.config(text=f"✅ {message}", foreground="green")
        else:
            result_label.config(text=f"❌ {message}", foreground="red")
            
    def test_sound_entry(self, sound_var):
        """اختبار ملف الصوت"""
        sound = sound_var.get().strip()
        if not sound:
            self.log_comment("❌ الرجاء اختيار ملف صوتي!")
            return
            
        success, message = self.test_sound(sound)
        self.log_comment(message)
        
        
    def test_selected_binding(self):
        """اختبار التعيين المحدد فقط"""
        selected_items = self.keys_tree.selection()
        if not selected_items:
            self.log_comment("⚠️ الرجاء تحديد تعيين للاختبار")
            return
        
        item_id = selected_items[0]
        if item_id not in self.key_bindings:
            self.log_comment("❌ التعيين المحدد غير موجود")
            return
        
        binding = self.key_bindings[item_id]
        keys = binding.get('keys', '')
        sound = binding.get('sound', '')
        
        # الحصول على اسم التعيين للعرض
        event_type = binding.get('event_type', 'gift')
        if event_type == "gift":
            gift_name = self.gifts_data.get(item_id, {}).get('name', item_id)
            binding_name = f"هدية: {gift_name}"
        elif event_type == "like":
            likes_count = binding.get('likes_count', 1)
            binding_name = f"إعجاب: {likes_count} لايك"
        elif event_type == "comment":
            comment_text = binding.get('comment_text', 'تعليق')
            binding_name = f"تعليق: {comment_text[:20]}..."
        elif event_type == "follow":
            binding_name = "متابعة جديدة"
        elif event_type == "share":
            binding_name = "مشاركة جديدة"
        else:
            binding_name = "حدث غير معروف"
        
        if not keys:
            self.log_comment(f"⚠️ لا توجد مفاتيح محددة لـ: {binding_name}")
            return
        
        # إظهار رسالة الانتظار
        self.log_comment(f"⏳ سيبدأ اختبار '{binding_name}' بعد 3 ثوانٍ...")
        
        def execute_test():
            # اختبار المفاتيح
            success, message = self.test_keys(keys)
            self.log_comment(f"🧪 {binding_name}: {message}")
            
            # تشغيل الصوت إذا كان موجوداً
            if sound and os.path.exists(sound):
                try:
                    threading.Thread(target=playsound, args=(sound,), daemon=True).start()
                    self.log_comment(f"🔊 تم تشغيل الصوت: {os.path.basename(sound)}")
                except Exception as e:
                    self.log_comment(f"❌ خطأ في تشغيل الصوت: {str(e)}")
            elif sound:
                self.log_comment(f"⚠️ ملف الصوت غير موجود: {sound}")
        
        # تنفيذ الاختبار بعد 3 ثوانٍ
        self.root.after(3000, execute_test)

    def test_all_bindings(self):
        """اختبار جميع التعيينات مع تأخير 3 ثواني بين كل اختبار"""
        if not self.key_bindings:
            self.log_comment("❌ لا توجد تعيينات لاختبارها!")
            return
            
        self.log_comment("🔄 بدء اختبار جميع التعيينات (سيتم الانتظار 3 ثواني بين كل اختبار)")
        
        # الحصول على قائمة التعيينات
        bindings_list = list(self.key_bindings.items())
        
        # دالة لاختبار التعيين التالي
        def test_next_binding(index=0):
            if index >= len(bindings_list):
                self.log_comment("✅ تم الانتهاء من اختبار جميع التعيينات")
                return
                
            binding_id, binding = bindings_list[index]
            keys = binding.get('keys', '')
            sound = binding.get('sound', '')
            
            if keys:
                self.log_comment(f"🔄 اختبار التعيين: {binding_id}")
                
                # اختبار المفاتيح
                success, message = self.test_keys(keys)
                if success:
                    self.log_comment(f"✅ اختبار المفاتيح: {message}")
                    
                    # تشغيل الصوت إذا كان موجودًا
                    if sound:
                        sound_success, sound_message = self.test_sound(sound)
                        if sound_success:
                            self.log_comment(f"✅ تشغيل الصوت: {sound_message}")
                        else:
                            self.log_comment(f"❌ خطأ في الصوت: {sound_message}")
                else:
                    self.log_comment(f"❌ اختبار المفاتيح: {message}")
            else:
                self.log_comment(f"⚠️ تخطي التعيين {binding_id} (لا توجد مفاتيح محددة)")
            
            # الانتقال إلى التعيين التالي بعد 3 ثواني
            self.root.after(3000, lambda: test_next_binding(index + 1))
        
        # بدء الاختبار
        test_next_binding()
        
    def save_key_binding_edit(self, item_id, event_type_var, gift_type_var, likes_count_var, comment_text_var, keys_entry, sound_var, custom_name_var, dialog):
        """حفظ تعديلات التعيين"""
        event_type = event_type_var.get()
        keys = keys_entry.get().strip()
        sound = sound_var.get().strip()
        
        if not keys:
            self.log_comment("❌ الرجاء إدخال المفاتيح!")
            return
        
        # تحديد معرف الحدث الجديد بناءً على نوعه
        new_item_id = item_id  # الافتراضي هو الحفاظ على نفس المعرف
        
        if event_type == "gift":
            selected_gift_name = gift_type_var.get().strip()
            if not selected_gift_name:
                self.log_comment("❌ الرجاء اختيار هدية!")
                return
            
            # استخراج معرف الهدية من الاسم
            for gid, gift in self.gifts_data.items():
                if f"{gift['name']} (ID: {gid})" == selected_gift_name:
                    new_item_id = gid
                    break
        elif event_type == "like":
            new_item_id = "like_event"
        elif event_type == "comment":
            new_item_id = "comment_event"
        elif event_type == "follow":
            new_item_id = "follow_event"
        elif event_type == "share":
            new_item_id = "share_event"
        
        # إذا تغير معرف الحدث، احذف القديم
        if new_item_id != item_id and new_item_id in self.key_bindings:
            self.log_comment("⚠️ هذا الحدث موجود بالفعل!")
            return
        
        # حذف التعيين القديم إذا تغير المعرف
        if new_item_id != item_id:
            del self.key_bindings[item_id]
        
        # تحديث القاموس
        binding_data = {
            'keys': keys,
            'sound': sound,
            'event_type': event_type,
            'custom_name': custom_name_var.get().strip()
        }
        
        # إضافة بيانات خاصة بنوع الحدث
        if event_type == "like":
            try:
                like_count = int(likes_count_var.get())
                if like_count <= 0:
                    like_count = 1
                binding_data['likes_count'] = like_count
            except ValueError:
                binding_data['likes_count'] = 1
        elif event_type == "comment":
            comment_text = comment_text_var.get().strip()
            if not comment_text:
                comment_text = "شكراً على تعليقك!"
            binding_data['comment_text'] = comment_text
        
        # حفظ البيانات
        self.key_bindings[new_item_id] = binding_data
        
        # تحديث الواجهة
        self.update_keys_display()
        self.save_profiles()
        
        # إغلاق النافذة
        dialog.destroy()
        
        self.log_comment(f"✅ تم تحديث التعيين بنجاح")
        
    def delete_key_binding(self, event=None):
        """حذف التعيين المحدد"""
        selected_items = self.keys_tree.selection()
        if not selected_items:
            self.log_comment("⚠️ الرجاء تحديد تعيين للحذف")
            return
            
        # حذف العناصر المحددة
        for item_id in selected_items:
            if item_id in self.key_bindings:
                del self.key_bindings[item_id]
                self.keys_tree.delete(item_id)
                self.log_comment(f"✅ تم حذف التعيين: {item_id}")
        
        # حفظ التغييرات
        self.save_profiles()
    
        
    def update_keys_display(self):
        """تحديث عرض المفاتيح في الواجهة"""
        # مسح القائمة الحالية
        for item in self.keys_tree.get_children():
            self.keys_tree.delete(item)
        for gift_id, binding in self.key_bindings.items():
            gift_name = self.gifts_data.get(gift_id, {}).get('name', gift_id)
            custom_name = binding.get('custom_name', binding.get('name', ''))
            keys = binding.get('keys', '')
            sound = binding.get('sound', '')
            
            # استخراج اسم الملف فقط من مسار الصوت
            if sound:
                sound = os.path.basename(sound)
            
            # تحديد نوع الحدث والتفاصيل
            event_type = binding.get('event_type', 'gift')
            
            # عمود الحدث: نوع الحدث فقط
            if event_type == "gift":
                event_type_display = "🎁 هدية"
                details = gift_name
                # إضافة صورة الهدية إذا كانت متوفرة
                if gift_id in self.gift_images:
                    # سنستخدم الصورة في العرض لاحقاً
                    pass
            elif event_type == "like":
                likes_count = binding.get('likes_count', 1)
                event_type_display = "❤️ إعجاب"
                details = f"يتطلب {likes_count} إعجاب"
            elif event_type == "comment":
                comment_text = binding.get('comment_text', 'تعليق')
                event_type_display = "💬 تعليق"
                details = f"نص: {comment_text}"
            elif event_type == "follow":
                event_type_display = "👤 متابعة"
                details = "متابع جديد"
            elif event_type == "share":
                event_type_display = "🔗 مشاركة"
                details = "مشاركة جديدة"
            else:
                event_type_display = "🔔 حدث"
                details = "حدث عام"
            
            # إضافة العنصر إلى القائمة
            # العمود الأول (#0): نوع الحدث
            # العمود الثاني (details): التفاصيل (اسم الهدية، نص التعليق، إلخ)
            # العمود الثالث (custom_name): الاسم المخصص
            # العمود الرابع (keys): المفاتيح
            # العمود الخامس (sound): الصوت
            self.keys_tree.insert('', 'end', iid=gift_id, 
                                values=(details, custom_name, keys, sound), 
                                text=event_type_display)
    
    def update_keys_from_text(self):
        """تحديث المفاتيح من النص المعروض"""
        try:
            text = self.keys_text.get('1.0', tk.END).strip()
            new_bindings = {}
            
            for line in text.split('\n'):
                if not line.strip():
                    continue
                
                try:
                    # تقسيم السطر إلى أجزاء
                    parts = [p.strip() for p in line.split('|')]
                    if len(parts) < 2:
                        continue
                    
                    # استخراج اسم الهدية
                    gift_name = parts[0].replace('الهدية:', '').strip()
                    
                    # استخراج المفاتيح
                    keys_part = next((p for p in parts if 'المفاتيح:' in p), None)
                    if not keys_part:
                        continue
                    keys = [k.strip() for k in keys_part.replace('المفاتيح:', '').split(',')]
                    
                    # استخراج الصوت (إذا وجد)
                    sound_part = next((p for p in parts if 'الصوت:' in p), None)
                    sound = sound_part.replace('الصوت:', '').strip() if sound_part else None
                    
                    # استخراج الاسم (إذا وجد)
                    name_part = next((p for p in parts if 'الاسم:' in p), None)
                    name = name_part.replace('الاسم:', '').strip() if name_part else None
                    
                    # تخزين البيانات
                    if sound:
                        new_bindings[gift_name] = {
                            'keys': keys,
                            'sound': sound,
                            'name': name
                        }
                    else:
                        new_bindings[gift_name] = {
                            'keys': keys,
                            'name': name
                        }
                    
                except Exception as e:
                    self.log_comment(f"❌ خطأ في معالجة السطر '{line}': {str(e)}")
                    continue
            
            # تحديث المفاتيح
            self.key_bindings = new_bindings
            
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحديث المفاتيح: {str(e)}")

    def log_comment(self, message):
        """تسجيل التعليق وقراءته"""
        # إضافة التعليق للعرض
        self.comments_queue.put(message)
        
        if not self.tts_enabled:
            return
            
        try:
            # استخراج نص التعليق فقط
            comment_match = re.search(r'\[(.*?)\] 💭 (.*?): (.*)', message)
            if comment_match:
                _, username, comment = comment_match.groups()
                if self.tts_language == 'ar':
                    text_to_speak = f"{username} يقول: {comment}"
                else:
                    text_to_speak = f"{username} says: {comment}"
                
                # إضافة التعليق لقائمة القراءة
                self.tts_queue.put(text_to_speak)
                
        except Exception as e:
            print(f"خطأ في معالجة التعليق: {str(e)}")

    def log_gift(self, message):
        self.gifts_queue.put(message)
    
    def process_messages(self):
        """معالجة الرسائل والهدايا من قوائم الانتظار"""
        try:
            # معالجة التعليقات للعرض
            while not self.comments_queue.empty():
                message = self.comments_queue.get()
                self.comments_area.configure(state='normal')
                self.comments_area.insert('1.0', message + '\n')
                self.comments_area.configure(state='disabled')
                self.comments_area.see('1.0')

            # معالجة قراءة التعليقات
            if not self.tts_queue.empty() and not self.is_speaking:
                text = self.tts_queue.get()
                self.is_speaking = True
                self.speak_text(text)
                self.is_speaking = False

            # معالجة الهدايا
            while not self.gifts_queue.empty():
                message = self.gifts_queue.get()
                self.gifts_area.configure(state='normal')
                self.gifts_area.insert('1.0', message + '\n')
                self.gifts_area.configure(state='disabled')
                self.gifts_area.see('1.0')

        except Exception as e:
            print(f"خطأ في معالجة الرسائل: {str(e)}")
        
        # جدولة المعالجة التالية
        self.root.after(50, self.process_messages)
    
    def simulate_keys(self, gift_id):
        """محاكاة ضغط المفاتيح للهدية المحددة"""
        if gift_id not in self.key_bindings:
            return
        
        binding = self.key_bindings[gift_id]
        keys_text = binding.get('keys', '')
        sound = binding.get('sound')
        
        # تشغيل الصوت إذا كان موجوداً
        if sound and os.path.exists(sound):
            threading.Thread(target=playsound, args=(sound,), daemon=True).start()
        
        # تقسيم النص إلى تسلسلات
        key_sequences = [k.strip() for k in keys_text.split(';')]
        
        # محاكاة المفاتيح بشكل متسلسل
        def simulate_sequence(index=0):
            if index >= len(key_sequences):
                self.log_comment("✅ تم محاكاة المفاتيح بنجاح!")
                return
            
            sequence = key_sequences[index]
            try:
                # استخراج المفتاح والتأخير
                if '@' in sequence:
                    key_part, delay = sequence.split('@')
                    delay = float(delay)
                else:
                    key_part = sequence
                    delay = 100  # تأخير افتراضي 100 مللي ثانية
                
                # معالجة المفاتيح المركبة
                if '+' in key_part:
                    key_parts = [k.strip().lower() for k in key_part.split('+')]
                    # الضغط على جميع المفاتيح
                    for k in key_parts[:-1]:
                        keyboard.press(k)
                    # الضغط والتحرير للمفتاح الأخير
                    keyboard.press_and_release(key_parts[-1])
                    # تحرير جميع المفاتيح
                    for k in reversed(key_parts[:-1]):
                        keyboard.release(k)
                else:
                    # مفتاح واحد
                    keyboard.press_and_release(key_part.lower())
                
                # جدولة التسلسل التالي
                self.root.after(int(delay), lambda: simulate_sequence(index + 1))
                
            except Exception as e:
                self.log_comment(f"❌ خطأ في محاكاة المفتاح {sequence}: {str(e)}")
                return
        
        # بدء محاكاة المفاتيح
        simulate_sequence()
    
    def toggle_tts(self):
        """تفعيل/تعطيل قراءة التعليقات"""
        self.tts_enabled = not self.tts_enabled
        if self.tts_enabled:
            self.tts_button.config(text="تعطيل القراءة", style='Danger.TButton')
        else:
            self.tts_button.config(text="تفعيل القراءة", style='Success.TButton')

    def update_tts_language(self):
        """تحديث لغة قراءة التعليقات"""
        self.tts_language = self.language_var.get()
        self.update_voice_list()  # تحديث قائمة الأصوات عند تغيير اللغة
        
    def update_voice_list(self):
        """تحديث قائمة الأصوات المتاحة"""
        voices = self.voices[self.tts_language]
        
        # تحديث القائمة المنسدلة بأسماء الأصوات
        self.voice_menu['values'] = list(voices.values())
        
        # تعيين الصوت الافتراضي
        default_voice = 'ar-EG-ShakirNeural' if self.tts_language == 'ar' else 'en-US-ChristopherNeural'
        self.selected_voice.set(default_voice)
        
        # عرض الاسم المقابل للصوت الافتراضي
        self.voice_menu.set(voices[default_voice])
        
        print(f"تم تحديث قائمة الأصوات. الصوت الافتراضي: {default_voice}")  # للتشخيص
        
        # ربط حدث تغيير القيمة
        self.voice_menu.bind('<<ComboboxSelected>>', self.on_voice_selected)

    def on_voice_selected(self, event):
        """معالجة اختيار صوت جديد"""
        display_name = self.voice_menu.get()
        
        # البحث عن معرف الصوت المقابل للاسم المعروض
        for voice_id, name in self.voices[self.tts_language].items():
            if name == display_name:
                self.selected_voice.set(voice_id)
                print(f"تم اختيار الصوت: {voice_id}")  # للتشخيص
                break

    def browse_sound_file(self, sound_var):
        """اختيار ملف صوت"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف الصوت",
            filetypes=[
                ("ملفات الصوت", "*.wav;*.mp3"),
                ("جميع الملفات", "*.*")
            ]
        )
        if file_path:
            try:
                # منح المستخدم خيار حفظ المسار كمسار مطلق أو نسخ الملف إلى مجلد الأصوات
                choice = messagebox.askyesno(
                    "حفظ المسار",
                    "هل ترغب في حفظ المسار المطلق للملف؟\n\n"
                    "نعم = حفظ المسار المطلق\nلا = نسخ الملف إلى مجلد الأصوات"
                )
                
                if choice:
                    # حفظ المسار المطلق
                    sound_var.set(file_path)
                    self.log_comment(f"✅ تم حفظ المسار المطلق: {file_path}")
                else:
                    # نسخ الملف إلى مجلد الأصوات
                    if not os.path.exists(self.sounds_dir):
                        os.makedirs(self.sounds_dir)
                    
                    sound_filename = os.path.basename(file_path)
                    target_path = os.path.join(self.sounds_dir, sound_filename)
                    
                    if not os.path.exists(target_path):
                        import shutil
                        shutil.copy2(file_path, target_path)
                    
                    # حفظ المسار النسبي
                    relative_path = os.path.join('sounds', sound_filename)
                    sound_var.set(relative_path)
                    self.log_comment(f"✅ تم نسخ الملف إلى: {relative_path}")
            except Exception as e:
                self.log_comment(f"❌ خطأ في اختيار ملف الصوت: {str(e)}")
    



            

    
    def save_profile(self):
        """حفظ البروفايل الحالي"""
        if not self.current_profile:
            # فتح نافذة لإدخال اسم البروفايل
            dialog = tk.Toplevel(self.root)
            dialog.title("حفظ البروفايل")
            dialog.geometry("300x150")
            dialog.resizable(False, False)
            dialog.configure(bg='#1e1e1e')  # إضافة خلفية داكنة
            
            # إطار الإدخال
            frame = ttk.Frame(dialog, padding="20")
            frame.pack(fill='both', expand=True)
            
            ttk.Label(frame, text="اسم البروفايل:").pack(pady=(0, 10))
            
            name_var = tk.StringVar()
            name_entry = ttk.Entry(frame, textvariable=name_var)
            name_entry.pack(fill='x', pady=(0, 20))
            name_entry.focus()
            
            def save():
                name = name_var.get().strip()
                if name:
                    # حفظ البروفايل
                    self.profiles[name] = {
                        'bindings': self.key_bindings.copy(),
                        'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    self.current_profile = name
                    
                    # تحديث الواجهة
                    self.update_profiles_list()
                    self.save_profiles()
                    
                    dialog.destroy()
                    self.log_comment(f"✅ تم حفظ البروفايل: {name}")
                else:
                    self.log_comment("❌ الرجاء إدخال اسم للبروفايل")
            
            # أزرار التحكم
            buttons_frame = ttk.Frame(frame)
            buttons_frame.pack(fill='x')
            
            ttk.Button(buttons_frame, text="حفظ", command=save).pack(side='right', padx=5)
            ttk.Button(buttons_frame, text="إلغاء", command=dialog.destroy).pack(side='right')
            
            # ربط مفتاح Enter بزر الحفظ
            dialog.bind('<Return>', lambda e: save())
            dialog.bind('<Escape>', lambda e: dialog.destroy())
            
            # جعل النافذة مركزية
            dialog.transient(self.root)
            dialog.grab_set()
            self.root.wait_window(dialog)
        else:
            # تحديث البروفايل الحالي
            self.profiles[self.current_profile] = {
                'bindings': self.key_bindings.copy(),
                'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            self.save_profiles()
            self.log_comment(f"✅ تم تحديث البروفايل: {self.current_profile}")

    def update_profile_name(self):
        """تحديث اسم البروفايل المعروض"""
        try:
            # تحديث عنوان النافذة
            title = "StreamTok V 2"
            if self.current_profile:
                title += f" - {self.current_profile}"
            self.root.title(title)
            
            # تحديث حقل اسم البروفايل إذا كان موجوداً
            if hasattr(self, 'profile_name_var'):
                self.profile_name_var.set(self.current_profile or "")
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحديث اسم البروفايل: {str(e)}")
    
    def __del__(self):
        """تنظيف الملفات المؤقتة وإغلاق حلقة الأحداث عند إغلاق البرنامج"""
        try:
            if hasattr(self, 'loop') and self.loop.is_running():
                self.loop.stop()
            if hasattr(self, 'loop') and not self.loop.is_closed():
                self.loop.close()
            
            if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
                for file in os.listdir(self.temp_dir):
                    try:
                        os.remove(os.path.join(self.temp_dir, file))
                    except:
                        pass
                os.rmdir(self.temp_dir)
        except:
            pass

    def speak_text(self, text):
        """قراءة النص بالصوت"""
        if not self.tts_enabled:
            return
            
        try:
            # تنظيف النص من الرموز التعبيرية
            clean_text = re.sub(r'[😊🎁💎✨⚡❌✅💭]', '', text)
            
            if self.tts_language == 'ar':
                # ترجمة الأرقام إلى كلمات عربية
                clean_text = self.convert_numbers_to_arabic_words(clean_text)
            
            print(f"جاري قراءة النص: {clean_text}")  # للتشخيص
            
            # تشغيل الصوت في خيط منفصل
            threading.Thread(target=self._run_async_speak, args=(clean_text,), daemon=True).start()
            
        except Exception as e:
            print(f"خطأ في قراءة النص: {str(e)}")
            traceback.print_exc()

    def convert_numbers_to_arabic_words(self, text):
        """تحويل الأرقام إلى كلمات عربية"""
        # قاموس الأرقام العربية
        numbers = {
            '0': 'صفر', '1': 'واحد', '2': 'اثنين', '3': 'ثلاثة', '4': 'أربعة',
            '5': 'خمسة', '6': 'ستة', '7': 'سبعة', '8': 'ثمانية', '9': 'تسعة'
        }
        
        # استبدال كل رقم بما يقابله من كلمات
        for num, word in numbers.items():
            text = text.replace(num, f' {word} ')
            
        return text

    def _run_async_speak(self, text):
        """تشغيل قراءة النص بشكل غير متزامن"""
        try:
            voice_id = self.selected_voice.get()
            print(f"معرف الصوت المستخدم: {voice_id}")  # للتشخيص
            
            # التحقق من صحة معرف الصوت
            if voice_id not in self.voices[self.tts_language]:
                # إذا كان المعرف غير صالح، استخدم الصوت الافتراضي
                voice_id = 'ar-EG-ShakirNeural' if self.tts_language == 'ar' else 'en-US-ChristopherNeural'
                self.selected_voice.set(voice_id)
                print(f"تم التحويل إلى الصوت الافتراضي: {voice_id}")
            
            communicate = edge_tts.Communicate(text, voice_id)
            temp_file = os.path.join(self.temp_dir, f'speech_{int(time.time())}.mp3')
            
            # استخدام asyncio بشكل صحيح
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # حفظ الصوت في ملف مؤقت
                loop.run_until_complete(communicate.save(temp_file))
                print(f"تم حفظ الصوت في: {temp_file}")  # للتشخيص
                
                # تشغيل الصوت وانتظار انتهائه
                playsound(temp_file, block=True)
                print("تم تشغيل الصوت")  # للتشخيص
                
                # حذف الملف المؤقت
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    
            finally:
                loop.close()
                self.is_speaking = False
                
        except Exception as e:
            print(f"خطأ في تشغيل الصوت: {str(e)}")
            self.is_speaking = False
            traceback.print_exc()
    
    def _get_gift_display_names(self):
        """الحصول على قائمة أسماء الهدايا للعرض"""
        if not self.gifts_data:
            return []
        display_names = []
        # إنشاء قائمة من الهدايا مع عدد الكوينات
        gifts_list = []
        for gift_id, gift in self.gifts_data.items():
            coin_count = gift.get('diamond_count', 0)
            gifts_list.append((coin_count, gift['name'], gift_id))
        
        # ترتيب الهدايا من الأصغر إلى الأكبر حسب عدد الكوينات
        gifts_list.sort(key=lambda x: x[0])
        
        # إنشاء قائمة الأسماء للعرض
        for coin_count, name, gift_id in gifts_list:
            display_names.append(f"{name} ({coin_count})")
        return display_names
    
    def _get_gift_display_names_with_id(self):
        """الحصول على قائمة أسماء الهدايا مع المعرفات للعرض"""
        if not self.gifts_data:
            return []
        gift_names = []
        for gid, gift in self.gifts_data.items():
            gift_names.append(f"{gift['name']} (ID: {gid})")
        return sorted(gift_names)
    
    def _update_gift_image(self):
        """تحديث صورة الهدية المعروضة بناء على الاختيار"""
        try:
            if not hasattr(self, 'gift_combobox') or not hasattr(self, 'gift_image_label'):
                return
                
            selected_gift_name = self.gift_combobox.get().strip()
            if not selected_gift_name or not self.gifts_data:
                self.gift_image_label.config(image="", text="🎁")
                return
                
            # استخراج اسم الهدية من النص المعروض (إزالة عدد الكوينات)
            gift_name_only = selected_gift_name
            if " (" in selected_gift_name and ")" in selected_gift_name:
                gift_name_only = selected_gift_name.split(" (")[0]
            
            # البحث عن معرف الهدية المطابق للاسم المختار
            for gift_id, gift_data in self.gifts_data.items():
                if gift_data['name'] == gift_name_only:
                    if gift_id in self.gift_images:
                        self.gift_image_label.config(image=self.gift_images[gift_id], text="")
                    else:
                        self.gift_image_label.config(image="", text="🎁")
                    break
            else:
                self.gift_image_label.config(image="", text="🎁")
                
        except Exception as e:
            if hasattr(self, 'gift_image_label'):
                self.gift_image_label.config(image="", text="🎁")
            print(f"خطأ في تحديث صورة الهدية: {str(e)}")
    
    def _refresh_gift_combo(self):
        """تحديث القائمة المنسدلة للهدايا"""
        try:
            if hasattr(self, 'gift_combobox'):
                current_selection = self.gift_combobox.get()
                self.gift_combobox['values'] = self._get_gift_display_names()
                
                # استعادة الاختيار إذا كان لا يزال متاحاً
                if current_selection in self.gift_combobox['values']:
                    self.gift_combobox.set(current_selection)
                elif self.gift_combobox['values']:
                    self.gift_combobox.set(self.gift_combobox['values'][0])
                
                self._update_gift_image()
        except Exception as e:
            print(f"خطأ في تحديث قائمة الهدايا: {str(e)}")

    def cleanup(self):
        """تنظيف الموارد عند إغلاق التطبيق"""
        try:
            if hasattr(self, 'client'):
                self.client.stop()
            
            if hasattr(self, 'loop') and self.loop:
                self.loop.close()
            
            if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
                for file in os.listdir(self.temp_dir):
                    try:
                        os.remove(os.path.join(self.temp_dir, file))
                    except:
                        pass
                os.rmdir(self.temp_dir)
        except:
            pass

    def save_key_bindings(self):
        """حفظ ربط المفاتيح في ملف"""
        try:
            # التأكد من وجود المجلد
            if not os.path.exists('config'):
                os.makedirs('config')
            
            # حفظ البيانات في ملف
            with open('config/key_bindings.json', 'w', encoding='utf-8') as f:
                json.dump(self.key_bindings, f, ensure_ascii=False, indent=4)
            
            # تحديث العرض
            self.update_keys_display()
            
            self.log_comment("✅ تم حفظ ربط المفاتيح بنجاح")
        except Exception as e:
            self.log_comment(f"❌ خطأ في حفظ ربط المفاتيح: {str(e)}")

    def load_key_bindings(self):
        """تحميل ربط المفاتيح من ملف"""
        try:
            if os.path.exists('config/key_bindings.json'):
                with open('config/key_bindings.json', 'r', encoding='utf-8') as f:
                    self.key_bindings = json.load(f)
                
                # تحديث العرض بعد التحميل
                self.update_keys_display()
                
                self.log_comment("✅ تم تحميل ربط المفاتيح بنجاح")
            else:
                self.key_bindings = {}
                self.log_comment("ℹ️ لم يتم العثور على ملف ربط المفاتيح")
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحميل ربط المفاتيح: {str(e)}")

    def load_profile(self, event=None):
        """تحميل البروفايل المحدد"""
        try:
            # الحصول على البروفايل المحدد
            selection = self.profiles_list.curselection()
            if not selection:
                self.log_comment("⚠️ الرجاء تحديد بروفايل")
                return
                
            # الحصول على اسم البروفايل
            name = self.profiles_list.get(selection[0])
            
            if name in self.profiles:
                # تحميل البيانات
                self.key_bindings = self.profiles[name]['bindings']
                self.current_profile = name
                
                # تحديث العرض
                self.update_keys_display()
                self.update_profile_name()
                
                self.log_comment(f"✅ تم تحميل البروفايل: {name}")
            else:
                self.log_comment("⚠️ لم يتم العثور على البروفايل")
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحميل البروفايل: {str(e)}")

    def update_profiles_list(self):
        """تحديث قائمة البروفايلات"""
        try:
            # مسح القائمة
            self.profiles_list.delete(0, tk.END)
            
            # إضافة البروفايلات
            for name in sorted(self.profiles.keys()):
                self.profiles_list.insert(tk.END, name)
                
            # تحديد البروفايل الحالي إذا وجد
            if self.current_profile:
                try:
                    index = sorted(self.profiles.keys()).index(self.current_profile)
                    self.profiles_list.selection_set(index)
                except ValueError:
                    pass
                
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحديث قائمة البروفايلات: {str(e)}")

    def test_keys(self, keys_text):
        """اختبار محاكاة المفاتيح"""
        try:
            # تقسيم النص إلى مفاتيح
            key_sequences = [k.strip() for k in keys_text.split(';')]
            
            for key_sequence in key_sequences:
                if not key_sequence:
                    continue
                    
                # تقسيم التسلسل إلى خطوات
                steps = [step.strip() for step in key_sequence.split(';')]
                
                for step in steps:
                    if not step:
                        continue
                        
                    # معالجة التأخير والمفتاح
                    parts = step.split('@')
                    key_part = parts[0].strip()
                    
                    # تنفيذ التأخير قبل الضغط
                    if len(parts) > 1:
                        try:
                            delay = int(parts[1])
                            if delay > 0:
                                time.sleep(delay / 1000)
                        except ValueError:
                            pass
                    
                    # معالجة المفاتيح المركبة
                    if '+' in key_part:
                        key_parts = [k.strip().lower() for k in key_part.split('+')]
                        # الضغط على جميع المفاتيح
                        for k in key_parts[:-1]:
                            keyboard.press(k)
                        # الضغط والتحرير للمفتاح الأخير
                        keyboard.press_and_release(key_parts[-1])
                        # تحرير جميع المفاتيح
                        for k in reversed(key_parts[:-1]):
                            keyboard.release(k)
                    else:
                        # مفتاح واحد
                        keyboard.press_and_release(key_part.lower())
            
            return True, "✅ تم اختبار المفاتيح بنجاح"
            
        except Exception as e:
            return False, f"❌ خطأ في اختبار المفاتيح: {str(e)}"

    def on_profile_selected(self):
        """معالجة حدث اختيار بروفايل"""
        selected = self.profiles_list.get()
        if selected:
            self.current_profile = selected
            self.key_bindings = self.profiles[selected]['bindings']
            self.update_keys_display()
            self.update_profile_name()
            self.log_comment(f"✅ تم تحميل البروفايل: {selected}")

    def delete_profile(self):
        """حذف البروفايل الحالي"""
        selected = self.profiles_list.get()
        if not selected:
            self.log_comment("⚠️ الرجاء اختيار بروفايل للحذف")
            return
            
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف البروفايل '{selected}'؟"):
            # حذف البروفايل
            del self.profiles[selected]
            
            # إعادة تعيين البروفايل الحالي إذا تم حذفه
            if self.current_profile == selected:
                self.current_profile = None
                self.key_bindings = {}
                self.update_keys_display()
                self.update_profile_name()
            
            # تحديث القائمة
            self.update_profiles_list()
            self.save_profiles()
            
            self.log_comment(f"✅ تم حذف البروفايل: {selected}")

    def test_sound(self, sound_file):
        """اختبار تشغيل الصوت"""
        try:
            if not sound_file:
                return False, "⚠️ الرجاء اختيار ملف صوتي"
                
            # التحقق من وجود الملف الصوتي
            if not os.path.exists(sound_file):
                return False, "⚠️ ملف الصوت غير موجود"
                
            # تشغيل الصوت في خيط منفصل
            threading.Thread(target=playsound, args=(sound_file,), daemon=True).start()
            return True, "✅ تم تشغيل الصوت بنجاح"
            
        except Exception as e:
            return False, f"❌ خطأ في تشغيل الصوت: {str(e)}"

    def manage_profiles(self):
        """إدارة البروفايلات"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إدارة البروفايلات")
        dialog.geometry("650x500")
        dialog.configure(bg='#1e1e1e')
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(dialog, style='Modern.TFrame', padding="20")
        main_frame.pack(fill='both', expand=True)

        # إطار معلومات البروفايل الحالي
        current_profile_frame = ttk.LabelFrame(main_frame, text="البروفايل الحالي", style='Modern.TLabelframe', padding="10")
        current_profile_frame.pack(fill='x', pady=(0, 10))
        
        # عرض اسم البروفايل الحالي
        current_profile_label = ttk.Label(current_profile_frame, 
            text=f"البروفايل النشط: {self.current_profile if self.current_profile else 'لا يوجد'}", 
            style='Modern.TLabel')
        current_profile_label.pack(fill='x', padx=5, pady=5)
        
        # عدد المفاتيح في البروفايل الحالي
        keys_count = 0
        if self.current_profile and self.current_profile in self.profiles:
            keys_count = len(self.profiles[self.current_profile].get('bindings', {}))
        
        self.keys_label = ttk.Label(current_profile_frame, 
            text=f"عدد المفاتيح المحفوظة: {keys_count}", 
            style='Modern.TLabel')
        self.keys_label.pack(fill='x', padx=5)

        # إطار قائمة البروفايلات
        profiles_frame = ttk.LabelFrame(main_frame, text="البروفايلات المتوفرة", style='Modern.TLabelframe', padding="10")
        profiles_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # إنشاء قائمة البروفايلات مع شريط التمرير
        profiles_list_frame = ttk.Frame(profiles_frame)
        profiles_list_frame.pack(fill='both', expand=True)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(profiles_list_frame)
        scrollbar.pack(side='right', fill='y')
        
        # قائمة البروفايلات
        profiles_listbox = tk.Listbox(profiles_list_frame, 
            bg='#2d2d2d', fg='white', 
            selectmode='single',
            yscrollcommand=scrollbar.set)
        profiles_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=profiles_listbox.yview)
        
        # تحديث قائمة البروفايلات
        for profile in sorted(self.profiles.keys()):
            profiles_listbox.insert('end', profile)
            if profile == self.current_profile:
                profiles_listbox.selection_set('end')

        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame, style='Modern.TFrame')
        buttons_frame.pack(fill='x', pady=10)

        def create_new_profile():
            """إنشاء بروفايل جديد"""
            name = simpledialog.askstring("إنشاء بروفايل", "أدخل اسم البروفايل الجديد:")
            if name:
                name = name.strip()
                if not name:
                    self.log_comment("❌ الرجاء إدخال اسم صحيح للبروفايل")
                    return
                if name in self.profiles:
                    self.log_comment("❌ هذا البروفايل موجود مسبقاً")
                    return
                
                # إنشاء بروفايل جديد
                self.profiles[name] = {'bindings': {}}
                self.current_profile = name
                self.key_bindings = {}
                self.save_profiles()
                
                # تحديث القائمة
                profiles_listbox.insert('end', name)
                profiles_listbox.selection_clear(0, 'end')
                profiles_listbox.selection_set('end')
                
                # تحديث العرض
                current_profile_label.config(text=f"البروفايل النشط: {name}")
                self.keys_label.config(text="عدد المفاتيح المحفوظة: 0")
                
                self.log_comment(f"✅ تم إنشاء البروفايل: {name}")
                
        def load_selected_profile():
            """تحميل البروفايل المحدد"""
            selection = profiles_listbox.curselection()
            if not selection:
                self.log_comment("⚠️ الرجاء تحديد بروفايل")
                return
            
            profile_name = profiles_listbox.get(selection[0])
            if profile_name in self.profiles:
                # حفظ البروفايل الحالي قبل التغيير
                if self.current_profile:
                    self.profiles[self.current_profile] = {
                        'bindings': self.key_bindings.copy()
                    }
                
                # تحميل البروفايل الجديد
                self.key_bindings = self.profiles[profile_name].get('bindings', {}).copy()
                self.current_profile = profile_name
                self.save_profiles()
                self.update_keys_display()
                
                # تحديث العرض
                current_profile_label.config(text=f"البروفايل النشط: {profile_name}")
                keys_count = len(self.key_bindings)
                self.keys_label.config(text=f"عدد المفاتيح المحفوظة: {keys_count}")
                
                self.log_comment(f"✅ تم تحميل البروفايل: {profile_name}")
                
        def delete_selected_profile():
            """حذف البروفايل المحدد"""
            selection = profiles_listbox.curselection()
            if not selection:
                self.log_comment("⚠️ الرجاء تحديد بروفايل للحذف")
                return
            
            profile_name = profiles_listbox.get(selection[0])
            if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف البروفايل '{profile_name}'؟"):
                if profile_name == self.current_profile:
                    self.current_profile = None
                    self.key_bindings = {}
                    self.update_keys_display()
                    current_profile_label.config(text="البروفايل النشط: لا يوجد")
                    self.keys_label.config(text="عدد المفاتيح المحفوظة: 0")
                
                del self.profiles[profile_name]
                self.save_profiles()
                profiles_listbox.delete(selection[0])
                self.log_comment(f"✅ تم حذف البروفايل: {profile_name}")

        def import_profiles():
            """استيراد البروفايلات"""
            file_path = filedialog.askopenfilename(
                filetypes=[("ملفات البروفايلات", "*.json"), ("جميع الملفات", "*.*")],
                title="استيراد البروفايلات"
            )
            if file_path:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        imported_profiles = json.load(f)
                    
                    # التحقق من صحة البيانات المستوردة
                    if not isinstance(imported_profiles, dict):
                        raise ValueError("تنسيق الملف غير صحيح")
                    
                    # دمج البروفايلات المستوردة مع الحالية
                    self.profiles.update(imported_profiles)
                    self.save_profiles()
                    
                    # تحديث القائمة
                    profiles_listbox.delete(0, 'end')
                    for profile in sorted(self.profiles.keys()):
                        profiles_listbox.insert('end', profile)
                    
                    self.log_comment("✅ تم استيراد البروفايلات بنجاح")
                except Exception as e:
                    self.log_comment(f"❌ خطأ في استيراد البروفايلات: {str(e)}")

        def export_profiles():
            """تصدير البروفايلات"""
            if not self.profiles:
                self.log_comment("⚠️ لا توجد بروفايلات للتصدير")
                return
            
            file_path = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("ملفات البروفايلات", "*.json"), ("جميع الملفات", "*.*")],
                title="تصدير البروفايلات"
            )
            if file_path:
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(self.profiles, f, ensure_ascii=False, indent=4)
                    self.log_comment("✅ تم تصدير البروفايلات بنجاح")
                except Exception as e:
                    self.log_comment(f"❌ خطأ في تصدير البروفايلات: {str(e)}")

        # أزرار التحكم
        ModernButton(buttons_frame, text="➕ إنشاء", command=create_new_profile,
                  style_name='Modern.TButton').pack(side='left', padx=5)
        
        ModernButton(buttons_frame, text="📂 تحميل", command=load_selected_profile,
                  style_name='Modern.TButton').pack(side='left', padx=5)
        
        ModernButton(buttons_frame, text="❌ حذف", command=delete_selected_profile,
                  style_name='Modern.TButton').pack(side='left', padx=5)
        
        ModernButton(buttons_frame, text="📥 استيراد", command=import_profiles,
                  style_name='Modern.TButton').pack(side='left', padx=5)
        
        ModernButton(buttons_frame, text="📤 تصدير", command=export_profiles,
                  style_name='Modern.TButton').pack(side='left', padx=5)
        
        ModernButton(buttons_frame, text="✖️ إغلاق", command=dialog.destroy,
                  style_name='Modern.TButton').pack(side='right', padx=5)

        # جعل النافذة مركزية
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.focus()
    
    def apply_dark_window_theme(self):
        """تطبيق النمط الأسود على النافذة الرئيسية"""
        try:
            # الحصول على handle النافذة باستخدام ctypes
            hwnd = ctypes.windll.user32.GetParent(self.root.winfo_id())
            if not hwnd:
                hwnd = self.root.winfo_id()
            
            # تفعيل النمط الداكن على شريط العنوان
            DWMWA_USE_IMMERSIVE_DARK_MODE = 20
            value = ctypes.c_int(1)  # تفعيل النمط الداكن
            
            # استدعاء DwmSetWindowAttribute لتطبيق النمط الداكن
            result = ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_USE_IMMERSIVE_DARK_MODE,
                ctypes.byref(value),
                ctypes.sizeof(value)
            )
            
            if result == 0:  # S_OK
                self.log_comment("✅ تم تطبيق النمط الأسود على النافذة بنجاح")
            else:
                self.log_comment(f"⚠️ لم يتم تطبيق النمط الأسود (رمز الخطأ: {result})")
                
        except Exception as e:
            self.log_comment(f"❌ خطأ في تطبيق النمط الأسود على النافذة: {str(e)}")
            # تجربة طريقة بديلة للأنظمة الأقدم
            try:
                # تطبيق النمط الداكن بطريقة بديلة
                hwnd = self.root.winfo_id()
                DWM_USE_IMMERSIVE_DARK_MODE = 20
                
                # تعيين القيمة للنمط الداكن
                value = ctypes.c_bool(True)
                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    hwnd,
                    DWM_USE_IMMERSIVE_DARK_MODE,
                    ctypes.byref(value),
                    ctypes.sizeof(value)
                )
                
                self.log_comment("✅ تم تطبيق النمط الأسود بالطريقة البديلة")
                
            except Exception as e2:
                self.log_comment(f"❌ فشل في تطبيق النمط الأسود: {str(e2)}")

def show_simple_license_dialog():
    """نافذة جميلة بألوان السماء الليلية لإدخال الترخيص"""
    dialog = tk.Tk()
    dialog.title("🌙 تفعيل البرنامج")
    dialog.geometry("550x650")
    dialog.resizable(False, False)
    
    # ألوان السماء الليلية
    night_sky = "#0f1419"        # أسود مزرق عميق
    dark_blue = "#1a2332"        # أزرق داكن
    midnight_blue = "#2d3748"    # أزرق منتصف الليل
    star_silver = "#e2e8f0"      # فضي كالنجوم
    moon_glow = "#4299e1"        # توهج القمر الأزرق
    aurora_green = "#48bb78"     # أخضر الشفق القطبي
    sunset_orange = "#ed8936"    # برتقالي الغروب
    
    dialog.configure(bg=night_sky)
    
    # توسيط النافذة
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (550 // 2)
    y = (dialog.winfo_screenheight() // 2) - (650 // 2)
    dialog.geometry(f"550x650+{x}+{y}")
    
    # الإطار الرئيسي
    main_frame = tk.Frame(dialog, bg=night_sky, padx=40, pady=30)
    main_frame.pack(fill='both', expand=True)
    
    # إطار العنوان مع تدرج
    header_frame = tk.Frame(main_frame, bg=night_sky)
    header_frame.pack(fill='x', pady=(0, 25))
    
    # أيقونة كبيرة
    icon_label = tk.Label(header_frame, text="🌟", font=('Arial', 42), 
                         bg=night_sky, fg=moon_glow)
    icon_label.pack()
    
    # العنوان الرئيسي
    title_label = tk.Label(header_frame, text="StreamTok v2", 
                          font=('Arial', 22, 'bold'), 
                          bg=night_sky, fg=star_silver)
    title_label.pack(pady=(8, 5))
    
    # العنوان الفرعي
    subtitle_label = tk.Label(header_frame, text="✨ يرجى إدخال مفتاح الترخيص للمتابعة ✨", 
                             font=('Arial', 11), 
                             bg=night_sky, fg="#a0aec0")
    subtitle_label.pack()
    
    # بطاقة معرف الجهاز
    device_card = tk.Frame(main_frame, bg=dark_blue, relief='flat', bd=0)
    device_card.pack(fill='x', pady=(0, 15))
    
    device_inner = tk.Frame(device_card, bg=dark_blue, padx=20, pady=15)
    device_inner.pack(fill='both', expand=True)
    
    device_label = tk.Label(device_inner, text="🖥️ معرف الجهاز (Device ID)", 
                           font=('Arial', 11, 'bold'), 
                           bg=dark_blue, fg=star_silver)
    device_label.pack(anchor='w', pady=(0, 8))
    
    machine_id = license_manager.get_machine_id()
    machine_entry = tk.Entry(device_inner, font=('Consolas', 12, 'bold'), 
                            bg=midnight_blue, fg=star_silver, 
                            relief='solid', bd=1, state='normal',
                            readonlybackground=midnight_blue,
                            insertbackground=star_silver)
    machine_entry.insert(0, machine_id)
    machine_entry.pack(fill='x', ipady=12, ipadx=15)
    
    # إضافة نص توضيحي
    info_label = tk.Label(device_inner, text="يرجى نسخ هذا المعرف أو أخذ لقطة شاشة له",
                         font=('Arial', 9),
                         bg=dark_blue, fg="#a0aec0")
    info_label.pack(anchor='w', pady=(5, 0))

    # بطاقة مفتاح الترخيص
    license_card = tk.Frame(main_frame, bg=dark_blue, relief='flat', bd=0)
    license_card.pack(fill='x', pady=(0, 15))
    
    license_inner = tk.Frame(license_card, bg=dark_blue, padx=20, pady=15)
    license_inner.pack(fill='both', expand=True)
    
    license_label = tk.Label(license_inner, text="🔑 مفتاح الترخيص", 
                            font=('Arial', 11, 'bold'), 
                            bg=dark_blue, fg=star_silver)
    license_label.pack(anchor='w', pady=(0, 8))
    
    license_entry = tk.Entry(license_inner, font=('Arial', 12), 
                            bg=midnight_blue, fg=star_silver, 
                            relief='flat', bd=0,
                            insertbackground=star_silver)
    license_entry.pack(fill='x', ipady=3, ipadx=12)
    
    # رسالة الحالة
    status_frame = tk.Frame(main_frame, bg=night_sky)
    status_frame.pack(fill='x', pady=(0, 20))
    
    status_label = tk.Label(status_frame, text="", font=('Arial', 10), 
                           bg=night_sky, fg="#fc8181")
    status_label.pack()
    
    result = {'valid': False}
    
    def verify_license():
        key = license_entry.get().strip()
        if not key:
            status_label.config(text="⚠️ يرجى إدخال مفتاح الترخيص", fg="#fc8181")
            return
        
        status_label.config(text="🔄 جاري التحقق من الترخيص...", fg=moon_glow)
        dialog.update()
        
        # محاكاة التحقق (يمكن استبدالها بالتحقق الحقيقي لاحقاً)
        if key == "DEMO-2025" or key == "TEST-KEY":
            status_label.config(text="✅ تم التحقق بنجاح! مرحباً بك", fg=aurora_green)
            result['valid'] = True
            dialog.after(2000, dialog.destroy)
        else:
            # محاولة التحقق عبر الخادم
            license_manager.set_license_key(key)
            success, message = license_manager.check_license_online()
            if success:
                status_label.config(text=f"✅ {message}", fg=aurora_green)
                result['valid'] = True
                dialog.after(2000, dialog.destroy)
            else:
                status_label.config(text=f"❌ {message}", fg="#fc8181")
    
    # الأزرار مع تصميم جميل
    buttons_frame = tk.Frame(main_frame, bg=night_sky)
    buttons_frame.pack(fill='x', pady=(15, 0))
    
    # زر التحقق
    verify_btn = tk.Button(buttons_frame, text="🔍 تحقق من الترخيص", 
                          font=('Arial', 11, 'bold'),
                          bg=moon_glow, fg="white", 
                          relief='flat', bd=0, padx=25, pady=12,
                          command=verify_license, cursor='hand2',
                          activebackground="#3182ce", activeforeground="white")
    verify_btn.pack(side='right', padx=(15, 0))
    
    # زر الإلغاء
    cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", 
                          font=('Arial', 11),
                          bg="#718096", fg="white", 
                          relief='flat', bd=0, padx=25, pady=12,
                          command=dialog.destroy, cursor='hand2',
                          activebackground="#4a5568", activeforeground="white")
    cancel_btn.pack(side='right')
    
    # معلومات إضافية
    info_frame = tk.Frame(main_frame, bg=night_sky)
    info_frame.pack(fill='x', pady=(20, 0))
    
    info_text = "💫 للحصول على مفتاح الترخيص، تواصل مع الدعم الفني"
    info_label = tk.Label(info_frame, text=info_text, 
                         font=('Arial', 9), 
                         bg=night_sky, fg="#a0aec0")
    info_label.pack()
    
    # التركيز على حقل الترخيص
    license_entry.focus()
    license_entry.bind('<Return>', lambda e: verify_license())
    
    # تأثيرات hover للأزرار
    def on_enter_verify(e):
        verify_btn.config(bg="#3182ce")
    
    def on_leave_verify(e):
        verify_btn.config(bg=moon_glow)
    
    def on_enter_cancel(e):
        cancel_btn.config(bg="#4a5568")
    
    def on_leave_cancel(e):
        cancel_btn.config(bg="#718096")
    
    verify_btn.bind("<Enter>", on_enter_verify)
    verify_btn.bind("<Leave>", on_leave_verify)
    cancel_btn.bind("<Enter>", on_enter_cancel)
    cancel_btn.bind("<Leave>", on_leave_cancel)
    
    dialog.mainloop()
    return result['valid']
    
    # الأزرار
    buttons_frame = tk.Frame(main_frame)
    buttons_frame.pack(fill='x', pady=(10, 0))
    
    verify_btn = tk.Button(buttons_frame, text="🔍 تحقق", font=('Arial', 10, 'bold'),
                          bg='#0078d4', fg='white', padx=20, pady=8,
                          command=verify_license)
    verify_btn.pack(side='right', padx=(10, 0))
    
    cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", font=('Arial', 10),
                          bg='#6c757d', fg='white', padx=20, pady=8,
                          command=dialog.destroy)
    cancel_btn.pack(side='right')
    
    # التركيز على حقل الترخيص
    license_entry.focus()
    license_entry.bind('<Return>', lambda e: verify_license())
    
    dialog.mainloop()
    return result['valid']

if __name__ == "__main__":
    # إظهار نافذة الترخيص أولاً
    license_valid = show_simple_license_dialog()
    
    if license_valid:
        # فتح البرنامج الرئيسي
        root = tk.Tk()
        app = StreamTokGUI(root)
        root.mainloop()
    else:
        # إغلاق البرنامج إذا لم يتم التحقق من الترخيص
        pass
