@echo off
echo Building StreamTok...

rem Clean previous builds
rmdir /s /q build dist
del /f /q *.spec

rem Install required packages
pip install -r requirements.txt

rem Build the executable
pyinstaller --noconfirm ^
    --clean ^
    --onefile ^
    --windowed ^
    --name "StreamTok" ^
    --add-data "sounds;sounds" ^
    --add-data "tiktok_gifts.json;." ^
    --add-data "profiles.json;." ^
    --hidden-import playsound ^
    --hidden-import keyboard ^
    --hidden-import edge_tts ^
    --hidden-import pyttsx3 ^
    --hidden-import pyttsx3.drivers ^
    --hidden-import pyttsx3.drivers.sapi5 ^
    --hidden-import tkinter ^
    --hidden-import tkinter.ttk ^
    --hidden-import asyncio ^
    --hidden-import websockets ^
    --hidden-import protobuf ^
    --hidden-import TikTokLive ^
    main.py

echo Build complete! Check the dist folder for StreamTok.exe
