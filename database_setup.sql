-- إنشا<PERSON> قاعدة البيانات وجدول التراخيص
-- database_setup.sql

CREATE DATABASE IF NOT EXISTS license_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE license_db;

-- جدول التراخيص
CREATE TABLE IF NOT EXISTS licenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    license_key VARCHAR(100) NOT NULL UNIQUE,
    app_name VARCHAR(50) NOT NULL DEFAULT 'TikTok_Live_Tool',
    machine_id VARCHAR(32) DEFAULT NULL,
    status ENUM('active', 'suspended', 'expired') DEFAULT 'active',
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP NULL DEFAULT NULL,
    user_email VARCHAR(100) DEFAULT NULL,
    notes TEXT DEFAULT NULL,
    
    INDEX idx_license_key (license_key),
    INDEX idx_machine_id (machine_id),
    INDEX idx_status (status),
    INDEX idx_expires (expires_at)
);

-- إدراج بعض التراخيص التجريبية
INSERT INTO licenses (license_key, expires_at, user_email, notes) VALUES
('DEMO-2025-ABCD-1234', '2025-12-31 23:59:59', '<EMAIL>', 'ترخيص تجريبي'),
('TEST-2025-EFGH-5678', '2025-06-30 23:59:59', '<EMAIL>', 'ترخيص اختبار'),
('PREMIUM-2025-IJKL-9012', '2025-12-31 23:59:59', '<EMAIL>', 'ترخيص مميز');

-- عرض التراخيص المُدرجة
SELECT * FROM licenses;