# دليل إعداد نظام الترخيص

## الخطوات المطلوبة:

### 1. إعداد الخادم والاستضافة

#### أ) احصل على استضافة ويب تدعم PHP و MySQL
- يمكن استخدام استضافة مشتركة رخيصة (2-5 دولار شهرياً)
- تأكد من دعم PHP 7.4+ و MySQL 5.7+

#### ب) إنشاء قاعدة البيانات
1. ادخل إلى لوحة تحكم الاستضافة (cPanel أو مشابه)
2. اذهب إلى "MySQL Databases" أو "قواعد البيانات"
3. أنشئ قاعدة بيانات جديدة باسم `license_db`
4. أنشئ مستخدم جديد وأعطه صلاحيات كاملة على القاعدة
5. استورد ملف `database_setup.sql` لإنشاء الجداول

#### ج) رفع ملف PHP
1. ارفع ملف `server_check.php` إلى المجلد الرئيسي للموقع
2. عدّل إعدادات قاعدة البيانات في الملف:
   ```php
   $host = 'localhost';           // عادة localhost
   $dbname = 'license_db';        // اسم قاعدة البيانات
   $username = 'your_db_user';    // اسم مستخدم قاعدة البيانات
   $password = 'your_db_password'; // كلمة مرور قاعدة البيانات
   ```

### 2. تحديث البرنامج

#### أ) تحديث رابط الخادم
في ملف `main.py`، ابحث عن هذا السطر:
```python
self.server_url = "https://your-domain.com/check.php"
```
وغيّره إلى رابط موقعك الفعلي:
```python
self.server_url = "https://yourwebsite.com/server_check.php"
```

### 3. إدارة التراخيص

#### أ) إضافة ترخيص جديد
```sql
INSERT INTO licenses (license_key, expires_at, user_email, notes) 
VALUES ('YOUR-LICENSE-KEY', '2025-12-31 23:59:59', '<EMAIL>', 'ملاحظات');
```

#### ب) عرض جميع التراخيص
```sql
SELECT license_key, machine_id, status, expires_at, last_used, user_email 
FROM licenses 
ORDER BY created_at DESC;
```

#### ج) حظر ترخيص
```sql
UPDATE licenses SET status = 'suspended' WHERE license_key = 'LICENSE-TO-BLOCK';
```

#### د) تمديد ترخيص
```sql
UPDATE licenses SET expires_at = '2026-12-31 23:59:59' WHERE license_key = 'LICENSE-TO-EXTEND';
```

### 4. اختبار النظام

#### أ) اختبار الاتصال
افتح المتصفح واذهب إلى: `https://yourwebsite.com/server_check.php`
يجب أن تظهر رسالة "INVALID" (هذا طبيعي)

#### ب) اختبار ترخيص
استخدم أداة مثل Postman أو curl:
```bash
curl -X POST https://yourwebsite.com/server_check.php \
  -d "machine_id=test123" \
  -d "license_key=DEMO-2025-ABCD-1234" \
  -d "app_name=TikTok_Live_Tool"
```
يجب أن تحصل على "VALID"

### 5. الأمان الإضافي (اختياري)

#### أ) إخفاء ملف PHP
- ضع الملف في مجلد فرعي مثل `/api/check.php`
- استخدم اسم ملف غير واضح مثل `verify_license.php`

#### ب) إضافة HTTPS
- تأكد من أن موقعك يستخدم SSL (https://)
- معظم الاستضافات توفر SSL مجاني

#### ج) حماية إضافية
- أضف rate limiting لمنع الطلبات المتكررة
- سجل محاولات الوصول المشبوهة
- استخدم User-Agent checking

### 6. مولد مفاتيح الترخيص

يمكنك استخدام هذا الكود لتوليد مفاتيح عشوائية:

```python
import random
import string

def generate_license_key():
    parts = []
    for i in range(4):
        part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        parts.append(part)
    return '-'.join(parts)

# مثال: ABCD-1234-EFGH-5678
print(generate_license_key())
```

### 7. مراقبة الاستخدام

```sql
-- عرض التراخيص النشطة
SELECT license_key, user_email, last_used, expires_at 
FROM licenses 
WHERE status = 'active' AND expires_at > NOW()
ORDER BY last_used DESC;

-- عرض التراخيص المنتهية الصلاحية
SELECT license_key, user_email, expires_at 
FROM licenses 
WHERE expires_at < NOW()
ORDER BY expires_at DESC;
```

## ملاحظات مهمة:

1. **احتفظ بنسخة احتياطية** من قاعدة البيانات دورياً
2. **غيّر كلمات المرور** بانتظام
3. **راقب السجلات** للتأكد من عدم وجود محاولات اختراق
4. **اختبر النظام** قبل توزيع البرنامج

## استكشاف الأخطاء:

- **"لا يمكن الاتصال بالخادم"**: تحقق من رابط الخادم والإنترنت
- **"خطأ في الخادم"**: تحقق من إعدادات قاعدة البيانات
- **"ترخيص غير صالح"**: تأكد من وجود الترخيص في قاعدة البيانات
- **"انتهت الصلاحية"**: تحقق من تاريخ انتهاء الترخيص