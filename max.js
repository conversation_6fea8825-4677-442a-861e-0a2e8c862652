export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;
    const method = request.method;
    
    // إنشاء مفتاح ترخيص جديد (للمالك فقط)
    if (path === '/admin/create-key' && method === 'POST') {
      // هنا يجب أن تتحقق من أنك المالك (مثلاً باستخدام كلمة سر)
      const body = await request.json();
      const adminSecret = body.adminSecret;
      
      // تحقق من كلمة السر (يجب أن تستخدم متغير بيئة آمن)
      if (adminSecret !== env.ADMIN_SECRET) {
        return new Response(JSON.stringify({status: 'unauthorized'}), {
          headers: {'Content-Type': 'application/json'},
          status: 401
        });
      }
      
      // إنشاء مفتاح ترخيص فريد
      const licenseKey = generateLicenseKey();
      const deviceId = body.deviceId;
      const expiryDays = body.expiryDays || 30;
      
      // حساب تاريخ الانتهاء
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + expiryDays);
      
      // حفظ المفتاح
      await env.LICENSE_KV.put(licenseKey, JSON.stringify({
        device: deviceId,
        activated: false,
        expiry: expiryDate.toISOString()
      }));
      
      return new Response(JSON.stringify({
        status: 'created',
        licenseKey: licenseKey,
        expiry: expiryDate.toISOString()
      }), {
        headers: {'Content-Type': 'application/json'},
        status: 200
      });
    }
    
    // تفعيل اشتراك جديد (للمستخدم)
    if (path === '/activate' && method === 'POST') {
      const body = await request.json();
      const licenseKey = body.licenseKey;
      const deviceId = body.deviceId;
      
      if (!licenseKey || !deviceId) {
        return new Response(JSON.stringify({status: 'error', message: 'Missing parameters'}), {
          headers: {'Content-Type': 'application/json'},
          status: 400
        });
      }
      
      const licenseData = await env.LICENSE_KV.get(licenseKey);
      
      if (!licenseData) {
        return new Response(JSON.stringify({status: 'invalid'}), {
          headers: {'Content-Type': 'application/json'},
          status: 200
        });
      }
      
      const parsedData = JSON.parse(licenseData);
      
      // تحديث معلومات التفعيل
      await env.LICENSE_KV.put(licenseKey, JSON.stringify({
        ...parsedData,
        device: deviceId,
        activated: true
      }));
      
      return new Response(JSON.stringify({status: 'activated'}), {
        headers: {'Content-Type': 'application/json'},
        status: 200
      });
    }
    
    // التحقق من الاشتراك (للتطبيق)
    if (path === '/check') {
      const licenseKey = url.searchParams.get('key');
      const deviceId = url.searchParams.get('device');
      
      if (!licenseKey || !deviceId) {
        return new Response(JSON.stringify({status: 'error', message: 'Missing parameters'}), {
          headers: {'Content-Type': 'application/json'},
          status: 400
        });
      }
      
      const licenseData = await env.LICENSE_KV.get(licenseKey);
      
      if (!licenseData) {
        return new Response(JSON.stringify({status: 'invalid'}), {
          headers: {'Content-Type': 'application/json'},
          status: 200
        });
      }
      
      const parsedData = JSON.parse(licenseData);
      
      // التحقق من تاريخ الانتهاء
      const now = new Date();
      const expiryDate = new Date(parsedData.expiry);
      
      if (now > expiryDate) {
        return new Response(JSON.stringify({status: 'expired'}), {
          headers: {'Content-Type': 'application/json'},
          status: 200
        });
      }
      
      // التحقق من تطابق الجهاز
      if (parsedData.device !== deviceId) {
        return new Response(JSON.stringify({status: 'device_mismatch'}), {
          headers: {'Content-Type': 'application/json'},
          status: 200
        });
      }
      
      return new Response(JSON.stringify({status: 'valid', expiry: parsedData.expiry}), {
        headers: {'Content-Type': 'application/json'},
        status: 200
      });
    }
    
    return new Response('Not Found', { status: 404 });
  }
};

// دالة لإنشاء مفتاح ترخيص فريد
function generateLicenseKey() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let key = '';
  for (let i = 0; i < 16; i++) {
    if (i > 0 && i % 4 === 0) key += '-';
    key += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return key;
}
