from PIL import Image, ImageDraw

# إنشاء صورة جديدة بخلفية شفافة
size = (256, 256)
image = Image.new('RGBA', size, (0, 0, 0, 0))
draw = ImageDraw.Draw(image)

# رسم دائرة خلفية
circle_color = (45, 45, 75, 255)  # لون داكن
draw.ellipse([(20, 20), (236, 236)], fill=circle_color)

# رسم حرف S بشكل جميل
text_color = (137, 180, 250, 255)  # لون أزرق فاتح
draw.text((85, 50), "S", fill=text_color, font=None, font_size=150)

# حفظ الصورة كأيقونة
image.save('app.ico', format='ICO', sizes=[(256, 256)])
